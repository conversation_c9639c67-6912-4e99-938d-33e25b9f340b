-- US-07: Suggestions Database Schema
-- ===================================
-- 
-- This file contains the PostgreSQL schema for the suggestions feature
-- in the Dr. Resume application. It includes tables for basic suggestions,
-- premium AI-powered suggestions, and suggestion categories.
-- 
-- Author: Dr. <PERSON>sume Development Team
-- Date: 2025-07-23
-- Version: 1.0.0

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for suggestions
CREATE TYPE suggestion_type AS ENUM (
    'missing_keyword',
    'skill_enhancement', 
    'experience_improvement',
    'format_optimization',
    'ats_optimization',
    'content_enhancement'
);

CREATE TYPE suggestion_priority AS ENUM (
    'low',
    'medium', 
    'high',
    'critical'
);

-- Suggestion Categories Table
-- ===========================
-- Stores categories for organizing suggestions
CREATE TABLE IF NOT EXISTS suggestion_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(20),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Basic Suggestions Table
-- =======================
-- Stores basic keyword suggestions generated from local analysis
CREATE TABLE IF NOT EXISTS suggestions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Foreign keys (references to other US tables)
    user_id UUID NOT NULL, -- References users table from US-01
    resume_id UUID NOT NULL, -- References resumes table from US-03
    job_description_id UUID NOT NULL, -- References job_descriptions table from US-04
    matching_score_id UUID, -- References matching_scores table from US-06
    
    -- Suggestion content
    suggestion_type suggestion_type NOT NULL,
    priority suggestion_priority DEFAULT 'medium',
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    
    -- Keywords and improvements (stored as JSON)
    missing_keywords JSONB DEFAULT '[]'::jsonb,
    suggested_keywords JSONB DEFAULT '[]'::jsonb,
    improvement_areas JSONB DEFAULT '{}'::jsonb,
    
    -- Metrics
    confidence_score DECIMAL(3,2) DEFAULT 0.0 CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    implementation_difficulty INTEGER DEFAULT 3 CHECK (implementation_difficulty >= 1 AND implementation_difficulty <= 5),
    expected_impact DECIMAL(5,2) DEFAULT 0.0 CHECK (expected_impact >= 0.0),
    
    -- Status tracking
    is_implemented BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT suggestions_user_resume_jd_unique UNIQUE (user_id, resume_id, job_description_id, suggestion_type, title)
);

-- Premium Suggestions Table
-- =========================
-- Stores premium AI-generated suggestions from OpenAPI
CREATE TABLE IF NOT EXISTS premium_suggestions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Foreign keys
    user_id UUID NOT NULL, -- References users table from US-01
    resume_id UUID NOT NULL, -- References resumes table from US-03
    job_description_id UUID NOT NULL, -- References job_descriptions table from US-04
    suggestion_id UUID, -- References suggestions table (optional link to basic suggestion)
    
    -- AI-generated content
    ai_analysis TEXT NOT NULL,
    detailed_recommendations JSONB DEFAULT '[]'::jsonb,
    personalized_tips JSONB DEFAULT '[]'::jsonb,
    industry_insights JSONB DEFAULT '{}'::jsonb,
    
    -- AI model information
    ai_model_used VARCHAR(100) DEFAULT 'gpt-3.5-turbo',
    ai_prompt_version VARCHAR(50) DEFAULT 'v1.0',
    ai_response_tokens INTEGER DEFAULT 0,
    ai_cost_estimate DECIMAL(10,6) DEFAULT 0.0,
    
    -- Enhanced metrics
    market_relevance_score DECIMAL(3,2) DEFAULT 0.0 CHECK (market_relevance_score >= 0.0 AND market_relevance_score <= 1.0),
    career_progression_impact DECIMAL(3,2) DEFAULT 0.0 CHECK (career_progression_impact >= 0.0 AND career_progression_impact <= 1.0),
    salary_impact_estimate DECIMAL(5,2) DEFAULT 0.0,
    
    -- Implementation guidance
    step_by_step_guide JSONB DEFAULT '[]'::jsonb,
    resources_links JSONB DEFAULT '[]'::jsonb,
    timeline_estimate VARCHAR(100),
    
    -- Quality metrics
    ai_confidence_score DECIMAL(3,2) DEFAULT 0.0 CHECK (ai_confidence_score >= 0.0 AND ai_confidence_score <= 1.0),
    human_review_score DECIMAL(3,2) CHECK (human_review_score IS NULL OR (human_review_score >= 0.0 AND human_review_score <= 1.0)),
    user_rating INTEGER CHECK (user_rating IS NULL OR (user_rating >= 1 AND user_rating <= 5)),
    
    -- Status tracking
    is_generated BOOLEAN DEFAULT FALSE,
    generation_error TEXT,
    is_reviewed BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    generated_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT premium_suggestions_user_resume_jd_unique UNIQUE (user_id, resume_id, job_description_id),
    CONSTRAINT premium_suggestions_suggestion_fk FOREIGN KEY (suggestion_id) REFERENCES suggestions(id) ON DELETE SET NULL
);

-- Indexes for Performance
-- =======================

-- Suggestions table indexes
CREATE INDEX IF NOT EXISTS idx_suggestions_user_id ON suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_resume_id ON suggestions(resume_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_job_description_id ON suggestions(job_description_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_type ON suggestions(suggestion_type);
CREATE INDEX IF NOT EXISTS idx_suggestions_priority ON suggestions(priority);
CREATE INDEX IF NOT EXISTS idx_suggestions_created_at ON suggestions(created_at);
CREATE INDEX IF NOT EXISTS idx_suggestions_confidence_score ON suggestions(confidence_score);
CREATE INDEX IF NOT EXISTS idx_suggestions_status ON suggestions(is_implemented, is_dismissed);

-- Premium suggestions table indexes
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_user_id ON premium_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_resume_id ON premium_suggestions(resume_id);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_job_description_id ON premium_suggestions(job_description_id);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_generated ON premium_suggestions(is_generated);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_created_at ON premium_suggestions(created_at);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_ai_model ON premium_suggestions(ai_model_used);

-- Suggestion categories indexes
CREATE INDEX IF NOT EXISTS idx_suggestion_categories_active ON suggestion_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_suggestion_categories_sort_order ON suggestion_categories(sort_order);

-- JSON indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_suggestions_missing_keywords ON suggestions USING GIN (missing_keywords);
CREATE INDEX IF NOT EXISTS idx_suggestions_suggested_keywords ON suggestions USING GIN (suggested_keywords);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_recommendations ON premium_suggestions USING GIN (detailed_recommendations);
CREATE INDEX IF NOT EXISTS idx_premium_suggestions_tips ON premium_suggestions USING GIN (personalized_tips);

-- Functions and Triggers
-- ======================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_suggestions_updated_at 
    BEFORE UPDATE ON suggestions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_premium_suggestions_updated_at 
    BEFORE UPDATE ON premium_suggestions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suggestion_categories_updated_at 
    BEFORE UPDATE ON suggestion_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for Common Queries
-- ========================

-- View for active suggestions with category information
CREATE OR REPLACE VIEW active_suggestions AS
SELECT 
    s.*,
    sc.name as category_name,
    sc.icon as category_icon,
    sc.color as category_color
FROM suggestions s
LEFT JOIN suggestion_categories sc ON sc.name = 
    CASE s.suggestion_type
        WHEN 'missing_keyword' THEN 'Missing Keywords'
        WHEN 'skill_enhancement' THEN 'Skills Enhancement'
        WHEN 'experience_improvement' THEN 'Experience Optimization'
        WHEN 'format_optimization' THEN 'Format Improvements'
        ELSE 'Other'
    END
WHERE s.is_dismissed = FALSE;

-- View for premium suggestions with metrics
CREATE OR REPLACE VIEW premium_suggestions_summary AS
SELECT 
    ps.*,
    CASE 
        WHEN ps.is_generated AND ps.generation_error IS NULL THEN 'completed'
        WHEN ps.generation_error IS NOT NULL THEN 'failed'
        ELSE 'pending'
    END as status,
    EXTRACT(EPOCH FROM (ps.generated_at - ps.created_at)) as generation_time_seconds
FROM premium_suggestions ps;

-- Sample Data for Development
-- ===========================

-- Insert default suggestion categories
INSERT INTO suggestion_categories (name, description, icon, color, sort_order) VALUES
('Missing Keywords', 'Keywords that appear in job description but missing from resume', 'fas fa-search', '#e74c3c', 1),
('Skills Enhancement', 'Suggestions for improving existing skills presentation', 'fas fa-cogs', '#3498db', 2),
('Experience Optimization', 'Ways to better present work experience and achievements', 'fas fa-briefcase', '#2ecc71', 3),
('Format Improvements', 'Resume formatting and structure suggestions', 'fas fa-file-alt', '#f39c12', 4),
('ATS Optimization', 'Suggestions for improving Applicant Tracking System compatibility', 'fas fa-robot', '#9b59b6', 5),
('Content Enhancement', 'General content improvement suggestions', 'fas fa-edit', '#34495e', 6)
ON CONFLICT (name) DO NOTHING;

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dr_resume_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO dr_resume_user;

-- Comments for documentation
COMMENT ON TABLE suggestions IS 'Stores basic keyword suggestions generated from local analysis';
COMMENT ON TABLE premium_suggestions IS 'Stores premium AI-generated suggestions from OpenAPI';
COMMENT ON TABLE suggestion_categories IS 'Categories for organizing suggestions';

COMMENT ON COLUMN suggestions.confidence_score IS 'Confidence score from 0.0 to 1.0 indicating reliability of suggestion';
COMMENT ON COLUMN suggestions.implementation_difficulty IS 'Difficulty rating from 1 (easy) to 5 (very hard)';
COMMENT ON COLUMN suggestions.expected_impact IS 'Expected improvement in matching score percentage';

COMMENT ON COLUMN premium_suggestions.ai_cost_estimate IS 'Estimated cost in USD for the AI API call';
COMMENT ON COLUMN premium_suggestions.ai_response_tokens IS 'Number of tokens used in the AI response';
COMMENT ON COLUMN premium_suggestions.market_relevance_score IS 'How relevant the suggestion is to current market trends';

-- End of schema
