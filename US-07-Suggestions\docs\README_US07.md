# US-07: Suggestions (Basic + Premium OpenAPI) - Complete Guide

## 📋 Overview

**US-07: Suggestions** is the intelligent recommendation engine of the Dr. Resume - AI Resume Scanner application. This user story implements both basic keyword suggestions using local algorithms and premium AI-powered suggestions using OpenAI API, providing users with actionable insights to improve their resume-job matching scores.

### 🎯 What This US Accomplishes

- ✅ **Basic Suggestions**: Local algorithm-based keyword and improvement suggestions
- ✅ **Premium AI Suggestions**: OpenAI-powered detailed recommendations and insights
- ✅ **Keyword Analysis**: Advanced NLP processing for missing keyword identification
- ✅ **Role-Based Access**: JWT-based premium feature access control
- ✅ **Interactive UI**: Modern responsive interface with suggestion management
- ✅ **Real-time Updates**: Dynamic suggestion status tracking and updates
- ✅ **Cost Tracking**: OpenAI API usage and cost monitoring
- ✅ **Comprehensive Testing**: Full test suite for all components

## 🏗️ Architecture Overview

```
US-07-Suggestions/
├── backend/                           # Flask API server
│   ├── us07_suggestions_model.py     # Database models for suggestions
│   ├── us07_suggestions_generator.py # Basic suggestion generation logic
│   ├── us07_openai_service.py        # Premium AI service integration
│   ├── us07_suggestions_routes.py    # API routes and endpoints
│   ├── us07_app.py                   # Main Flask application
│   └── requirements.txt              # Python dependencies
├── frontend/                         # HTML/CSS/JS client
│   ├── us07_suggestions.html         # Main suggestions interface
│   └── us07_suggestions.js           # Frontend logic and API calls
├── database/                         # Database setup
│   ├── us07_schema.sql              # PostgreSQL schema with indexes
│   └── us07_init_db.py              # Database initialization script
├── tests/                           # Test suite
│   └── test_us07_suggestions.py     # Comprehensive test coverage
└── docs/                           # Documentation
    └── README_US07.md              # This file
```

## 🚀 Quick Start Guide

### Prerequisites

1. **Python 3.9+** installed
2. **PostgreSQL 13+** running
3. **OpenAI API Key** (for premium features)
4. **Previous US completed** (US-01 through US-06)
5. **Web browser** for testing frontend

### Step 1: Database Setup

1. **Ensure PostgreSQL is running** (from previous US setup)

2. **Run Database Schema**:
   ```bash
   cd US-07-Suggestions/database
   psql -U dr_resume_user -d dr_resume_db -f us07_schema.sql
   ```

3. **Initialize Database with Sample Data**:
   ```bash
   python us07_init_db.py --sample-data
   ```

### Step 2: Backend Setup

1. **Create Virtual Environment**:
   ```bash
   cd US-07-Suggestions/backend
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment Variables**:
   Create `.env` file in backend directory:
   ```env
   # Database Configuration (from previous US)
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=dr_resume_db
   DB_USER=dr_resume_user
   DB_PASSWORD=your_secure_password
   
   # Flask Configuration
   SECRET_KEY=your-super-secret-key-change-in-production
   JWT_SECRET_KEY=your-jwt-secret-key
   FLASK_ENV=development
   
   # OpenAI Configuration (for premium features)
   OPENAI_API_KEY=your-openai-api-key-here
   OPENAI_MODEL=gpt-3.5-turbo
   OPENAI_MAX_TOKENS=2000
   OPENAI_TEMPERATURE=0.7
   
   # Rate Limiting
   REDIS_URL=memory://
   
   # CORS Configuration
   CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
   ```

4. **Download Required NLP Models**:
   ```bash
   python -m spacy download en_core_web_sm
   python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"
   ```

5. **Start Backend Server**:
   ```bash
   python us07_app.py
   ```

   Server will start at: `http://localhost:5007`

### Step 3: Frontend Setup

1. **Open Frontend**:
   ```bash
   cd US-07-Suggestions/frontend
   
   # Using Python's built-in server
   python -m http.server 3000
   
   # Then open: http://localhost:3000/us07_suggestions.html
   ```

## 🧪 Testing

### Run Backend Tests

```bash
cd US-07-Suggestions/tests
pytest test_us07_suggestions.py -v
```

### Manual Testing Checklist

1. **Basic Suggestions Flow**:
   - [ ] Open `http://localhost:3000/us07_suggestions.html`
   - [ ] Verify basic suggestions load automatically
   - [ ] Check suggestion cards display properly
   - [ ] Test "Mark as Implemented" functionality
   - [ ] Test "Dismiss" functionality
   - [ ] Verify confidence scores and impact indicators

2. **Premium Suggestions Flow**:
   - [ ] Click "Generate Premium Suggestions" button
   - [ ] Verify premium access check works
   - [ ] Check AI-generated suggestions display
   - [ ] Verify detailed recommendations format
   - [ ] Test personalized tips section

3. **API Testing**:
   ```bash
   # Test basic suggestions endpoint
   curl -X GET http://localhost:5007/api/suggestions/sample-resume-456/sample-jd-789 \
     -H "Authorization: Bearer your-jwt-token"
   
   # Test premium suggestions endpoint
   curl -X POST http://localhost:5007/api/premium-suggestions \
     -H "Authorization: Bearer your-premium-jwt-token" \
     -H "Content-Type: application/json" \
     -d '{
       "resume_id": "sample-resume-456",
       "job_description_id": "sample-jd-789"
     }'
   
   # Test suggestion status update
   curl -X PUT http://localhost:5007/api/suggestions/suggestion-id/status \
     -H "Authorization: Bearer your-jwt-token" \
     -H "Content-Type: application/json" \
     -d '{"is_implemented": true}'
   ```

## 📚 Learning Guide for Beginners

### Understanding the Flow

1. **User accesses suggestions page** → Frontend loads with authentication
2. **Basic suggestions generated** → Local algorithms analyze keyword gaps
3. **Suggestions displayed** → Interactive cards with actions and metrics
4. **Premium suggestions** → AI-powered detailed analysis (if premium user)
5. **User interactions** → Mark implemented/dismissed, view details
6. **Status updates** → Real-time tracking of suggestion progress

### Key Concepts Explained

#### 1. **Basic vs Premium Suggestions**
```python
# Basic suggestions use local algorithms
basic_suggestions = suggestions_generator.generate_suggestions(
    resume_keywords, jd_keywords, resume_text, jd_text
)

# Premium suggestions use OpenAI API
premium_suggestions = await openai_service.generate_premium_suggestions(
    resume_text, jd_text, matching_score, user_id
)
```

#### 2. **Keyword Analysis with NLP**
```python
# Extract keywords using spaCy and NLTK
def extract_keywords(self, text: str) -> Set[str]:
    doc = self.nlp(text.lower())
    keywords = set()
    
    for token in doc:
        if (not token.is_stop and not token.is_punct and 
            len(token.text) >= 2 and token.pos_ in ['NOUN', 'ADJ', 'VERB']):
            keywords.add(token.lemma_)
    
    return keywords
```

#### 3. **OpenAI Integration Pattern**
```python
# Async API call with retry logic and rate limiting
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def generate_premium_suggestions(self, resume_text: str, job_description: str):
    await self._enforce_rate_limit()
    
    response = self.client.chat.completions.create(
        model=self.model,
        messages=[
            {"role": "system", "content": "You are an expert career coach..."},
            {"role": "user", "content": prompt}
        ],
        response_format={"type": "json_object"}
    )
    
    return self._parse_response(response)
```

#### 4. **Role-Based Access Control**
```python
def check_premium_access(user_claims: Dict) -> bool:
    """Check if user has premium access"""
    return user_claims.get('role') == 'premium' or user_claims.get('is_premium', False)

@jwt_required()
def generate_premium_suggestions():
    user_claims = get_jwt()
    if not check_premium_access(user_claims):
        return jsonify({'upgrade_required': True}), 403
```

#### 5. **Database Design for Suggestions**
```sql
-- Basic suggestions table
CREATE TABLE suggestions (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    suggestion_type suggestion_type NOT NULL,
    missing_keywords JSONB DEFAULT '[]'::jsonb,
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    is_implemented BOOLEAN DEFAULT FALSE
);

-- Premium suggestions with AI metadata
CREATE TABLE premium_suggestions (
    id UUID PRIMARY KEY,
    ai_analysis TEXT NOT NULL,
    ai_model_used VARCHAR(100) DEFAULT 'gpt-3.5-turbo',
    ai_cost_estimate DECIMAL(10,6) DEFAULT 0.0
);
```

## 🔧 Configuration Reference

### Environment Variables

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `OPENAI_API_KEY` | OpenAI API key for premium features | `sk-...` | Yes (Premium) |
| `OPENAI_MODEL` | OpenAI model to use | `gpt-3.5-turbo` | No |
| `OPENAI_MAX_TOKENS` | Maximum tokens per request | `2000` | No |
| `OPENAI_TEMPERATURE` | AI creativity level (0-1) | `0.7` | No |
| `OPENAI_RPM_LIMIT` | Requests per minute limit | `20` | No |

### API Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/suggestions/<resume_id>/<jd_id>` | Get basic suggestions | JWT |
| `POST` | `/api/premium-suggestions` | Generate premium AI suggestions | JWT + Premium |
| `GET` | `/api/premium-suggestions/<id>` | Get premium suggestion details | JWT + Premium |
| `PUT` | `/api/suggestions/<id>/status` | Update suggestion status | JWT |
| `GET` | `/api/health` | Service health check | None |

### Suggestion Types

| Type | Description | Priority | Implementation |
|------|-------------|----------|----------------|
| `missing_keyword` | Keywords in JD but not in resume | High | Add to skills/experience |
| `skill_enhancement` | Improve existing skill presentation | Medium | Reorganize sections |
| `experience_improvement` | Add quantifiable achievements | Medium | Rewrite with metrics |
| `format_optimization` | Improve resume structure | Low | Format changes |
| `ats_optimization` | Improve ATS compatibility | High | Keyword placement |

## 🐛 Troubleshooting

### Common Issues

1. **OpenAI API Error**:
   ```
   Error: Invalid API key provided
   ```
   **Solution**: Verify `OPENAI_API_KEY` in `.env` file

2. **NLP Model Not Found**:
   ```
   OSError: Can't find model 'en_core_web_sm'
   ```
   **Solution**: Install spaCy model: `python -m spacy download en_core_web_sm`

3. **Premium Access Denied**:
   ```
   403: Premium access required
   ```
   **Solution**: Use premium JWT token or update user role in token claims

4. **Database Connection Error**:
   ```
   Error: relation "suggestions" does not exist
   ```
   **Solution**: Run database initialization: `python us07_init_db.py`

5. **Rate Limit Exceeded**:
   ```
   429: Rate limit exceeded
   ```
   **Solution**: Adjust `OPENAI_RPM_LIMIT` or implement request queuing

### Debug Mode

Enable detailed logging:
```python
# In us07_app.py
app.config['SQLALCHEMY_ECHO'] = True  # Log SQL queries
logging.getLogger('us07_openai_service').setLevel(logging.DEBUG)
app.run(debug=True)
```

## 🔄 Next Steps

After completing US-07, you're ready for:

- **US-08**: Dashboard (Scan History) - View all past suggestions and analyses
- **US-09**: API Protection - Enhanced security and rate limiting
- **US-10**: Account Settings - User profile and premium subscription management

## 📖 Additional Resources

- [OpenAI API Documentation](https://platform.openai.com/docs)
- [spaCy NLP Library](https://spacy.io/usage/spacy-101)
- [NLTK Documentation](https://www.nltk.org/)
- [Flask-JWT-Extended Guide](https://flask-jwt-extended.readthedocs.io/)
- [PostgreSQL JSON Functions](https://www.postgresql.org/docs/current/functions-json.html)

---

**🎉 Congratulations!** You've successfully implemented US-07: Suggestions with both basic and premium AI-powered features. This intelligent system will help users significantly improve their resume-job matching scores through actionable, data-driven recommendations.
