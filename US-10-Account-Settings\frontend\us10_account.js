/**
 * US-10: Account Settings Frontend JavaScript
 * ===========================================
 * 
 * This file handles account settings management including:
 * - Profile information updates
 * - Email and password changes with re-authentication
 * - User preferences management
 * - Form validation and submission
 * 
 * Author: Dr. Resume Development Team
 * Date: 2025-07-23
 * Version: 1.0.0
 */

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://localhost:5010/api',
    ENDPOINTS: {
        PROFILE: '/account/profile',
        UPDATE_ACCOUNT: '/account/update_account',
        CHANGE_PASSWORD: '/account/change-password',
        PREFERENCES: '/account/preferences'
    },
    STORAGE_KEYS: {
        AUTH_TOKEN: 'dr_resume_token',
        USER_DATA: 'dr_resume_user'
    }
};

// Global state
let authToken = null;
let currentProfile = null;
let currentPreferences = null;

// DOM elements
let elements = {};

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeAuth();
    setupEventListeners();
    loadAccountData();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        // Forms
        profileForm: document.getElementById('profileForm'),
        accountUpdateForm: document.getElementById('accountUpdateForm'),
        passwordChangeForm: document.getElementById('passwordChangeForm'),
        preferencesForm: document.getElementById('preferencesForm'),
        
        // Profile fields
        firstName: document.getElementById('firstName'),
        lastName: document.getElementById('lastName'),
        displayName: document.getElementById('displayName'),
        email: document.getElementById('email'),
        bio: document.getElementById('bio'),
        jobTitle: document.getElementById('jobTitle'),
        company: document.getElementById('company'),
        
        // Account update fields
        newEmail: document.getElementById('newEmail'),
        currentPasswordForAccount: document.getElementById('currentPasswordForAccount'),
        
        // Password change fields
        currentPassword: document.getElementById('currentPassword'),
        newPassword: document.getElementById('newPassword'),
        confirmPassword: document.getElementById('confirmPassword'),
        strengthBar: document.getElementById('strengthBar'),
        strengthText: document.getElementById('strengthText'),
        passwordMatch: document.getElementById('passwordMatch'),
        
        // Preference fields
        emailNotifications: document.getElementById('emailNotifications'),
        marketingEmails: document.getElementById('marketingEmails'),
        securityAlerts: document.getElementById('securityAlerts'),
        theme: document.getElementById('theme'),
        itemsPerPage: document.getElementById('itemsPerPage'),
        autoSave: document.getElementById('autoSave'),
        
        // UI elements
        alertContainer: document.getElementById('alertContainer'),
        loadingOverlay: document.getElementById('loadingOverlay'),
        logoutBtn: document.getElementById('logoutBtn')
    };
}

/**
 * Initialize authentication
 */
function initializeAuth() {
    authToken = localStorage.getItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    
    if (!authToken) {
        // Redirect to login or create test token
        createTestToken();
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Form submissions
    elements.profileForm.addEventListener('submit', handleProfileSubmit);
    elements.accountUpdateForm.addEventListener('submit', handleAccountUpdateSubmit);
    elements.passwordChangeForm.addEventListener('submit', handlePasswordChangeSubmit);
    elements.preferencesForm.addEventListener('submit', handlePreferencesSubmit);
    
    // Password strength checking
    elements.newPassword.addEventListener('input', checkPasswordStrength);
    elements.confirmPassword.addEventListener('input', checkPasswordMatch);
    
    // Logout
    elements.logoutBtn.addEventListener('click', logout);
}

/**
 * Load account data
 */
async function loadAccountData() {
    try {
        showLoading(true);
        
        // Load profile
        await loadProfile();
        
        // Load preferences
        await loadPreferences();
        
    } catch (error) {
        console.error('Error loading account data:', error);
        showAlert('Failed to load account data. Please refresh the page.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Load user profile
 */
async function loadProfile() {
    try {
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.PROFILE);
        const data = await response.json();
        
        if (data.success) {
            currentProfile = data.data;
            populateProfileForm(currentProfile);
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error loading profile:', error);
        // Create empty profile for new users
        currentProfile = {};
    }
}

/**
 * Load user preferences
 */
async function loadPreferences() {
    try {
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.PREFERENCES);
        const data = await response.json();
        
        if (data.success) {
            currentPreferences = data.data;
            populatePreferencesForm(currentPreferences);
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error loading preferences:', error);
        // Use default preferences
        currentPreferences = {};
    }
}

/**
 * Populate profile form with data
 */
function populateProfileForm(profile) {
    elements.firstName.value = profile.first_name || '';
    elements.lastName.value = profile.last_name || '';
    elements.displayName.value = profile.display_name || '';
    elements.email.value = '<EMAIL>'; // Simulated email
    elements.bio.value = profile.bio || '';
    elements.jobTitle.value = profile.job_title || '';
    elements.company.value = profile.company || '';
}

/**
 * Populate preferences form with data
 */
function populatePreferencesForm(preferences) {
    // Notifications
    elements.emailNotifications.checked = preferences.notifications?.email_notifications ?? true;
    elements.marketingEmails.checked = preferences.notifications?.marketing_emails ?? false;
    elements.securityAlerts.checked = preferences.notifications?.security_alerts ?? true;
    
    // Application
    elements.theme.value = preferences.application?.theme || 'light';
    elements.itemsPerPage.value = preferences.application?.items_per_page || 10;
    elements.autoSave.checked = preferences.application?.auto_save ?? true;
}

/**
 * Handle profile form submission
 */
async function handleProfileSubmit(event) {
    event.preventDefault();
    
    try {
        showLoading(true);
        
        const formData = new FormData(elements.profileForm);
        const profileData = Object.fromEntries(formData.entries());
        
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.PROFILE, {
            method: 'PUT',
            body: JSON.stringify(profileData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            currentProfile = data.data;
            showAlert('Profile updated successfully!', 'success');
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error updating profile:', error);
        showAlert('Failed to update profile. Please try again.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Handle account update form submission (email/password with re-authentication)
 */
async function handleAccountUpdateSubmit(event) {
    event.preventDefault();
    
    try {
        showLoading(true);
        
        const formData = new FormData(elements.accountUpdateForm);
        const accountData = Object.fromEntries(formData.entries());
        
        // Validate current password is provided
        if (!accountData.current_password) {
            showAlert('Current password is required for security verification.', 'warning');
            return;
        }
        
        // Only include email if it's provided
        const updateData = {
            current_password: accountData.current_password
        };
        
        if (accountData.email && accountData.email.trim()) {
            updateData.email = accountData.email.trim();
        }
        
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.UPDATE_ACCOUNT, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Account updated successfully!', 'success');
            
            // Clear form
            elements.accountUpdateForm.reset();
            
            // Show additional info
            if (data.data.email_verification_sent) {
                showAlert('Email verification sent to your new email address.', 'info');
            }
            
            if (data.data.requires_re_login) {
                showAlert('Please log in again with your new credentials.', 'warning');
            }
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error updating account:', error);
        showAlert('Failed to update account. Please check your current password and try again.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Handle password change form submission
 */
async function handlePasswordChangeSubmit(event) {
    event.preventDefault();
    
    try {
        // Validate passwords match
        if (elements.newPassword.value !== elements.confirmPassword.value) {
            showAlert('New passwords do not match.', 'warning');
            return;
        }
        
        // Validate password strength
        const strength = getPasswordStrength(elements.newPassword.value);
        if (strength < 3) {
            showAlert('Please choose a stronger password.', 'warning');
            return;
        }
        
        showLoading(true);
        
        const passwordData = {
            current_password: elements.currentPassword.value,
            new_password: elements.newPassword.value,
            confirm_password: elements.confirmPassword.value
        };
        
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.CHANGE_PASSWORD, {
            method: 'POST',
            body: JSON.stringify(passwordData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('Password changed successfully!', 'success');
            
            // Clear form
            elements.passwordChangeForm.reset();
            elements.strengthBar.className = 'strength-bar';
            elements.strengthText.textContent = 'Enter a password';
            elements.passwordMatch.textContent = '';
            
            if (data.data.requires_re_login) {
                showAlert('Please log in again with your new password.', 'info');
            }
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error changing password:', error);
        showAlert('Failed to change password. Please check your current password and try again.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Handle preferences form submission
 */
async function handlePreferencesSubmit(event) {
    event.preventDefault();
    
    try {
        showLoading(true);
        
        const preferencesData = {
            email_notifications: elements.emailNotifications.checked,
            marketing_emails: elements.marketingEmails.checked,
            security_alerts: elements.securityAlerts.checked,
            theme: elements.theme.value,
            items_per_page: parseInt(elements.itemsPerPage.value),
            auto_save: elements.autoSave.checked
        };
        
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.PREFERENCES, {
            method: 'PUT',
            body: JSON.stringify(preferencesData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            currentPreferences = data.data;
            showAlert('Preferences updated successfully!', 'success');
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error updating preferences:', error);
        showAlert('Failed to update preferences. Please try again.', 'danger');
    } finally {
        showLoading(false);
    }
}

/**
 * Check password strength
 */
function checkPasswordStrength() {
    const password = elements.newPassword.value;
    const strength = getPasswordStrength(password);
    
    // Update strength bar
    elements.strengthBar.className = 'strength-bar';
    
    if (password.length === 0) {
        elements.strengthText.textContent = 'Enter a password';
        return;
    }
    
    switch (strength) {
        case 1:
            elements.strengthBar.classList.add('strength-weak');
            elements.strengthText.textContent = 'Weak password';
            elements.strengthText.className = 'text-danger';
            break;
        case 2:
            elements.strengthBar.classList.add('strength-fair');
            elements.strengthText.textContent = 'Fair password';
            elements.strengthText.className = 'text-warning';
            break;
        case 3:
            elements.strengthBar.classList.add('strength-good');
            elements.strengthText.textContent = 'Good password';
            elements.strengthText.className = 'text-info';
            break;
        case 4:
            elements.strengthBar.classList.add('strength-strong');
            elements.strengthText.textContent = 'Strong password';
            elements.strengthText.className = 'text-success';
            break;
    }
}

/**
 * Get password strength score
 */
function getPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++;
    
    return Math.min(score, 4);
}

/**
 * Check if passwords match
 */
function checkPasswordMatch() {
    const newPassword = elements.newPassword.value;
    const confirmPassword = elements.confirmPassword.value;
    
    if (confirmPassword.length === 0) {
        elements.passwordMatch.textContent = '';
        return;
    }
    
    if (newPassword === confirmPassword) {
        elements.passwordMatch.textContent = 'Passwords match';
        elements.passwordMatch.className = 'text-success';
    } else {
        elements.passwordMatch.textContent = 'Passwords do not match';
        elements.passwordMatch.className = 'text-danger';
    }
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    elements.alertContainer.innerHTML = alertHtml;
    
    // Auto-dismiss success alerts
    if (type === 'success') {
        setTimeout(() => {
            const alert = elements.alertContainer.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
}

/**
 * Get alert icon based on type
 */
function getAlertIcon(type) {
    const icons = {
        success: 'check-circle',
        danger: 'exclamation-triangle',
        warning: 'exclamation-circle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * Show/hide loading overlay
 */
function showLoading(show) {
    elements.loadingOverlay.style.display = show ? 'flex' : 'none';
}

/**
 * Make authenticated request
 */
async function makeAuthenticatedRequest(endpoint, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    const response = await fetch(`${CONFIG.API_BASE_URL}${endpoint}`, mergedOptions);
    
    if (response.status === 401) {
        // Token expired, redirect to login
        logout();
        throw new Error('Authentication required');
    }
    
    return response;
}

/**
 * Create test token for development
 */
async function createTestToken() {
    try {
        const response = await fetch('http://localhost:5010/api/dev/create-test-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: 'test-user-123',
                is_premium: true
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);
        }
    } catch (error) {
        console.error('Error creating test token:', error);
    }
}

/**
 * Logout user
 */
function logout() {
    localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_DATA);
    window.location.href = '../US-01-User-Registration/frontend/us01_home.html';
}
