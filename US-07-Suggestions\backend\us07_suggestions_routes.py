"""
US-07: Suggestions Routes
=========================

This module defines the API routes for the suggestions feature in the Dr. Resume application.
It includes routes for both basic keyword suggestions and premium AI-powered suggestions.

Routes:
- GET /api/suggestions/<resume_id>/<jd_id> - Get basic suggestions
- POST /api/premium-suggestions - Generate premium AI suggestions
- GET /api/premium-suggestions/<suggestion_id> - Get premium suggestion details
- PUT /api/suggestions/<suggestion_id>/status - Update suggestion status

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from sqlalchemy.exc import SQLAlchemyError
from marshmallow import Schema, fields, ValidationError

# Local imports
from us07_suggestions_model import db, Suggestion, PremiumSuggestion, SuggestionType, SuggestionPriority
from us07_suggestions_generator import SuggestionsGenerator
from us07_openai_service import get_openai_service, ResponseParser

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Blueprint
suggestions_bp = Blueprint('suggestions', __name__)

# Initialize services
suggestions_generator = SuggestionsGenerator()


# Validation Schemas
class SuggestionRequestSchema(Schema):
    """Schema for validating suggestion requests"""
    resume_id = fields.Str(required=True)
    job_description_id = fields.Str(required=True)


class PremiumSuggestionRequestSchema(Schema):
    """Schema for validating premium suggestion requests"""
    resume_id = fields.Str(required=True)
    job_description_id = fields.Str(required=True)
    include_skill_analysis = fields.Bool(missing=True)
    include_experience_optimization = fields.Bool(missing=True)


class SuggestionStatusSchema(Schema):
    """Schema for updating suggestion status"""
    is_implemented = fields.Bool(missing=None)
    is_dismissed = fields.Bool(missing=None)
    user_rating = fields.Int(missing=None, validate=lambda x: 1 <= x <= 5)


# Helper Functions
def check_premium_access(user_claims: Dict) -> bool:
    """Check if user has premium access"""
    return user_claims.get('role') == 'premium' or user_claims.get('is_premium', False)


def get_user_keywords_and_text(resume_id: str, jd_id: str) -> tuple:
    """Get keywords and text for resume and job description"""
    try:
        # This would typically query the database for resume and JD data
        # For now, we'll return mock data - in real implementation, 
        # this would fetch from US-03, US-04, and US-05 data
        
        # Mock data - replace with actual database queries
        resume_keywords = {'python', 'flask', 'sql', 'javascript', 'html', 'css'}
        jd_keywords = {'python', 'django', 'postgresql', 'react', 'aws', 'docker', 'kubernetes'}
        resume_text = "Software Developer with 3 years experience in Python and web development..."
        jd_text = "We are looking for a Senior Python Developer with experience in Django, PostgreSQL..."
        
        return resume_keywords, jd_keywords, resume_text, jd_text
    except Exception as e:
        logger.error(f"Error fetching keywords and text: {str(e)}")
        raise


# Routes
@suggestions_bp.route('/api/suggestions/<resume_id>/<jd_id>', methods=['GET'])
@jwt_required()
def get_basic_suggestions(resume_id: str, jd_id: str):
    """
    Get basic keyword suggestions for a resume and job description pair
    
    This endpoint generates suggestions by analyzing the gap between
    resume keywords and job description keywords using local algorithms.
    """
    try:
        user_id = get_jwt_identity()
        
        # Validate input
        if not resume_id or not jd_id:
            return jsonify({
                'success': False,
                'message': 'Resume ID and Job Description ID are required'
            }), 400
        
        # Get keywords and text
        resume_keywords, jd_keywords, resume_text, jd_text = get_user_keywords_and_text(resume_id, jd_id)
        
        # Check if suggestions already exist
        existing_suggestions = Suggestion.query.filter_by(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=jd_id
        ).all()
        
        if existing_suggestions:
            # Return existing suggestions
            suggestions_data = [suggestion.to_dict() for suggestion in existing_suggestions]
        else:
            # Generate new suggestions
            raw_suggestions = suggestions_generator.generate_suggestions(
                resume_keywords, jd_keywords, resume_text, jd_text
            )
            
            # Save suggestions to database
            suggestions_data = []
            for raw_suggestion in raw_suggestions:
                suggestion = Suggestion(
                    user_id=user_id,
                    resume_id=resume_id,
                    job_description_id=jd_id,
                    suggestion_type=SuggestionType(raw_suggestion['type']),
                    priority=SuggestionPriority(raw_suggestion['priority']),
                    title=raw_suggestion['title'],
                    description=raw_suggestion['description'],
                    missing_keywords=raw_suggestion.get('missing_keywords', []),
                    suggested_keywords=raw_suggestion.get('suggested_keywords', []),
                    confidence_score=raw_suggestion.get('confidence_score', 0.0),
                    implementation_difficulty=raw_suggestion.get('implementation_difficulty', 3),
                    expected_impact=raw_suggestion.get('expected_impact', 0.0)
                )
                
                db.session.add(suggestion)
                suggestions_data.append(suggestion.to_dict())
            
            db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Basic suggestions retrieved successfully',
            'data': {
                'suggestions': suggestions_data,
                'total_count': len(suggestions_data),
                'resume_id': resume_id,
                'job_description_id': jd_id
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting basic suggestions: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve suggestions',
            'error': str(e)
        }), 500


@suggestions_bp.route('/api/premium-suggestions', methods=['POST'])
@jwt_required()
def generate_premium_suggestions():
    """
    Generate premium AI-powered suggestions using OpenAI API
    
    This endpoint requires premium access and generates detailed,
    personalized suggestions using artificial intelligence.
    """
    try:
        user_id = get_jwt_identity()
        user_claims = get_jwt()
        
        # Check premium access
        if not check_premium_access(user_claims):
            return jsonify({
                'success': False,
                'message': 'Premium access required for AI-powered suggestions',
                'upgrade_required': True
            }), 403
        
        # Validate request data
        schema = PremiumSuggestionRequestSchema()
        try:
            data = schema.load(request.json)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Invalid request data',
                'errors': e.messages
            }), 400
        
        resume_id = data['resume_id']
        jd_id = data['job_description_id']
        
        # Check if premium suggestions already exist
        existing_premium = PremiumSuggestion.query.filter_by(
            user_id=user_id,
            resume_id=resume_id,
            job_description_id=jd_id
        ).first()
        
        if existing_premium and existing_premium.is_generated:
            return jsonify({
                'success': True,
                'message': 'Premium suggestions already exist',
                'data': existing_premium.to_dict()
            }), 200
        
        # Get keywords and text
        resume_keywords, jd_keywords, resume_text, jd_text = get_user_keywords_and_text(resume_id, jd_id)
        
        # Calculate current matching score (mock - would come from US-06)
        matching_score = 75.5
        
        # Generate AI suggestions
        openai_service = get_openai_service()
        
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            ai_response = loop.run_until_complete(
                openai_service.generate_premium_suggestions(
                    resume_text, jd_text, matching_score, user_id
                )
            )
        finally:
            loop.close()
        
        # Create or update premium suggestion record
        if existing_premium:
            premium_suggestion = existing_premium
        else:
            premium_suggestion = PremiumSuggestion(
                user_id=user_id,
                resume_id=resume_id,
                job_description_id=jd_id
            )
        
        # Update with AI response
        premium_suggestion.ai_analysis = ai_response.get('overall_assessment', '')
        premium_suggestion.detailed_recommendations = ai_response.get('improvement_areas', [])
        premium_suggestion.personalized_tips = ai_response.get('personalized_tips', [])
        premium_suggestion.industry_insights = ai_response.get('industry_insights', {})
        premium_suggestion.ai_model_used = ai_response.get('metadata', {}).get('model_used', 'gpt-3.5-turbo')
        premium_suggestion.ai_response_tokens = ai_response.get('metadata', {}).get('tokens_used', 0)
        premium_suggestion.ai_cost_estimate = ai_response.get('metadata', {}).get('cost_estimate', 0.0)
        premium_suggestion.is_generated = True
        premium_suggestion.generated_at = datetime.utcnow()
        
        if not existing_premium:
            db.session.add(premium_suggestion)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Premium suggestions generated successfully',
            'data': premium_suggestion.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error generating premium suggestions: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Failed to generate premium suggestions',
            'error': str(e)
        }), 500


@suggestions_bp.route('/api/premium-suggestions/<suggestion_id>', methods=['GET'])
@jwt_required()
def get_premium_suggestion_details(suggestion_id: str):
    """Get detailed premium suggestion by ID"""
    try:
        user_id = get_jwt_identity()
        user_claims = get_jwt()
        
        # Check premium access
        if not check_premium_access(user_claims):
            return jsonify({
                'success': False,
                'message': 'Premium access required',
                'upgrade_required': True
            }), 403
        
        # Get premium suggestion
        premium_suggestion = PremiumSuggestion.query.filter_by(
            id=suggestion_id,
            user_id=user_id
        ).first()
        
        if not premium_suggestion:
            return jsonify({
                'success': False,
                'message': 'Premium suggestion not found'
            }), 404
        
        return jsonify({
            'success': True,
            'message': 'Premium suggestion retrieved successfully',
            'data': premium_suggestion.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting premium suggestion details: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve premium suggestion',
            'error': str(e)
        }), 500


@suggestions_bp.route('/api/suggestions/<suggestion_id>/status', methods=['PUT'])
@jwt_required()
def update_suggestion_status(suggestion_id: str):
    """Update the status of a suggestion (implemented, dismissed, rated)"""
    try:
        user_id = get_jwt_identity()
        
        # Validate request data
        schema = SuggestionStatusSchema()
        try:
            data = schema.load(request.json)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Invalid request data',
                'errors': e.messages
            }), 400
        
        # Get suggestion
        suggestion = Suggestion.query.filter_by(
            id=suggestion_id,
            user_id=user_id
        ).first()
        
        if not suggestion:
            return jsonify({
                'success': False,
                'message': 'Suggestion not found'
            }), 404
        
        # Update status fields
        if 'is_implemented' in data:
            suggestion.is_implemented = data['is_implemented']
        
        if 'is_dismissed' in data:
            suggestion.is_dismissed = data['is_dismissed']
        
        suggestion.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Suggestion status updated successfully',
            'data': suggestion.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating suggestion status: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Failed to update suggestion status',
            'error': str(e)
        }), 500


@suggestions_bp.route('/api/suggestions/health', methods=['GET'])
def health_check():
    """Health check endpoint for suggestions service"""
    return jsonify({
        'success': True,
        'message': 'Suggestions service is healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'service': 'US-07 Suggestions'
    }), 200
