"""
US-09: Security Routes
======================

This module defines the API routes for the security and API protection features.
It includes routes for security monitoring, API key management, and security configuration.

Routes:
- GET /api/security/dashboard - Security dashboard overview
- GET /api/security/events - Security events with filtering
- GET /api/security/api-keys - API key management
- POST /api/security/api-keys - Create new API key
- GET /api/security/rules - Security rules configuration
- PUT /api/security/rules/<id> - Update security rule

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, asc, and_, or_, func
from marshmallow import Schema, fields, ValidationError

# Local imports
from us09_security_model import (
    db, SecurityEvent, SecurityEventType, SecurityEventSeverity,
    RateLimitRecord, APIKey, APIKeyStatus, AuditLog, SecurityRule
)
from us09_auth_decorators import (
    jwt_required_with_protection, require_premium_access, require_admin_access,
    protect_basic_route, protect_premium_route, protect_admin_route,
    get_current_user_info, check_token_validity
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Blueprint
security_bp = Blueprint('security', __name__)


# Validation Schemas
class SecurityDashboardSchema(Schema):
    """Schema for security dashboard request"""
    days = fields.Int(missing=7, validate=lambda x: 1 <= x <= 90)


class SecurityEventsFilterSchema(Schema):
    """Schema for security events filtering"""
    page = fields.Int(missing=1, validate=lambda x: x >= 1)
    per_page = fields.Int(missing=20, validate=lambda x: 1 <= x <= 100)
    event_type = fields.Str(missing=None)
    severity = fields.Str(missing=None)
    date_from = fields.DateTime(missing=None)
    date_to = fields.DateTime(missing=None)
    ip_address = fields.Str(missing=None)
    is_blocked = fields.Bool(missing=None)


class APIKeyCreateSchema(Schema):
    """Schema for creating API keys"""
    key_name = fields.Str(required=True, validate=lambda x: 1 <= len(x) <= 100)
    key_description = fields.Str(missing='')
    scopes = fields.List(fields.Str(), missing=list)
    allowed_endpoints = fields.List(fields.Str(), missing=list)
    rate_limit_per_hour = fields.Int(missing=1000, validate=lambda x: x > 0)
    rate_limit_per_day = fields.Int(missing=10000, validate=lambda x: x > 0)
    expires_in_days = fields.Int(missing=None, validate=lambda x: x > 0)
    allowed_ips = fields.List(fields.Str(), missing=list)


class SecurityRuleUpdateSchema(Schema):
    """Schema for updating security rules"""
    is_enabled = fields.Bool(missing=None)
    max_requests = fields.Int(missing=None, validate=lambda x: x > 0)
    time_window_seconds = fields.Int(missing=None, validate=lambda x: x > 0)
    rule_config = fields.Dict(missing=None)
    priority = fields.Int(missing=None, validate=lambda x: x > 0)


# Helper Functions
def check_admin_access():
    """Check if current user has admin access"""
    claims = get_jwt()
    return claims.get('role') == 'admin' or claims.get('is_admin', False)


def log_audit_event(action: str, resource_type: str, resource_id: str = None, 
                   old_values: Dict = None, new_values: Dict = None, success: bool = True):
    """Log audit event"""
    try:
        audit_log = AuditLog(
            user_id=get_jwt_identity(),
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            request_method=request.method,
            request_path=request.path,
            old_values=old_values or {},
            new_values=new_values or {},
            success=success
        )
        db.session.add(audit_log)
        db.session.commit()
    except Exception as e:
        logger.error(f"Failed to log audit event: {str(e)}")


# Routes
@security_bp.route('/api/security/dashboard', methods=['GET'])
@require_admin_access
def get_security_dashboard():
    """
    Get security dashboard overview with key metrics and recent activity
    
    Query Parameters:
    - days: Number of days to include in analysis (default: 7)
    """
    try:
        # Check admin access
        if not check_admin_access():
            return jsonify({
                'success': False,
                'message': 'Admin access required',
                'error': 'insufficient_permissions'
            }), 403
        
        # Validate query parameters
        schema = SecurityDashboardSchema()
        try:
            args = schema.load(request.args)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Invalid query parameters',
                'errors': e.messages
            }), 400
        
        days = args['days']
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Calculate security metrics
        total_events = SecurityEvent.query.filter(
            SecurityEvent.created_at >= start_date
        ).count()
        
        high_severity_events = SecurityEvent.query.filter(
            SecurityEvent.created_at >= start_date,
            SecurityEvent.severity.in_([SecurityEventSeverity.HIGH, SecurityEventSeverity.CRITICAL])
        ).count()
        
        blocked_requests = SecurityEvent.query.filter(
            SecurityEvent.created_at >= start_date,
            SecurityEvent.is_blocked == True
        ).count()
        
        # Top threat types
        threat_types = db.session.query(
            SecurityEvent.event_type,
            func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.created_at >= start_date
        ).group_by(SecurityEvent.event_type).order_by(desc('count')).limit(5).all()
        
        # Top attacking IPs
        attacking_ips = db.session.query(
            SecurityEvent.ip_address,
            func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.created_at >= start_date,
            SecurityEvent.severity.in_([SecurityEventSeverity.HIGH, SecurityEventSeverity.CRITICAL])
        ).group_by(SecurityEvent.ip_address).order_by(desc('count')).limit(10).all()
        
        # Recent critical events
        recent_critical = SecurityEvent.query.filter(
            SecurityEvent.created_at >= start_date,
            SecurityEvent.severity == SecurityEventSeverity.CRITICAL
        ).order_by(desc(SecurityEvent.created_at)).limit(5).all()
        
        # API key statistics
        total_api_keys = APIKey.query.count()
        active_api_keys = APIKey.query.filter_by(status=APIKeyStatus.ACTIVE).count()
        
        dashboard_data = {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'summary': {
                'total_security_events': total_events,
                'high_severity_events': high_severity_events,
                'blocked_requests': blocked_requests,
                'block_rate': round((blocked_requests / max(total_events, 1)) * 100, 2),
                'total_api_keys': total_api_keys,
                'active_api_keys': active_api_keys
            },
            'threat_analysis': {
                'top_threat_types': [
                    {'type': threat.event_type.value, 'count': threat.count}
                    for threat in threat_types
                ],
                'top_attacking_ips': [
                    {'ip': str(ip.ip_address), 'count': ip.count}
                    for ip in attacking_ips
                ]
            },
            'recent_critical_events': [
                {
                    'id': str(event.id),
                    'event_type': event.event_type.value,
                    'ip_address': str(event.ip_address),
                    'description': event.event_description,
                    'created_at': event.created_at.isoformat()
                }
                for event in recent_critical
            ]
        }
        
        return jsonify({
            'success': True,
            'message': 'Security dashboard data retrieved successfully',
            'data': dashboard_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting security dashboard: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve security dashboard',
            'error': str(e)
        }), 500


@security_bp.route('/api/security/events', methods=['GET'])
@require_admin_access
def get_security_events():
    """
    Get security events with filtering and pagination
    
    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20)
    - event_type: Filter by event type
    - severity: Filter by severity level
    - date_from: Filter from date
    - date_to: Filter to date
    - ip_address: Filter by IP address
    - is_blocked: Filter by blocked status
    """
    try:
        # Check admin access
        if not check_admin_access():
            return jsonify({
                'success': False,
                'message': 'Admin access required',
                'error': 'insufficient_permissions'
            }), 403
        
        # Validate query parameters
        schema = SecurityEventsFilterSchema()
        try:
            filters = schema.load(request.args)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Invalid query parameters',
                'errors': e.messages
            }), 400
        
        # Build query
        query = SecurityEvent.query
        
        # Apply filters
        if filters['event_type']:
            try:
                event_type = SecurityEventType(filters['event_type'])
                query = query.filter(SecurityEvent.event_type == event_type)
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': f"Invalid event type: {filters['event_type']}"
                }), 400
        
        if filters['severity']:
            try:
                severity = SecurityEventSeverity(filters['severity'])
                query = query.filter(SecurityEvent.severity == severity)
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': f"Invalid severity: {filters['severity']}"
                }), 400
        
        if filters['date_from']:
            query = query.filter(SecurityEvent.created_at >= filters['date_from'])
        
        if filters['date_to']:
            query = query.filter(SecurityEvent.created_at <= filters['date_to'])
        
        if filters['ip_address']:
            query = query.filter(SecurityEvent.ip_address == filters['ip_address'])
        
        if filters['is_blocked'] is not None:
            query = query.filter(SecurityEvent.is_blocked == filters['is_blocked'])
        
        # Order by creation date (newest first)
        query = query.order_by(desc(SecurityEvent.created_at))
        
        # Paginate
        page = filters['page']
        per_page = filters['per_page']
        
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        events = [event.to_dict() for event in pagination.items]
        
        return jsonify({
            'success': True,
            'message': 'Security events retrieved successfully',
            'data': {
                'events': events,
                'pagination': {
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'current_page': pagination.page,
                    'per_page': pagination.per_page,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                },
                'filters_applied': filters
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting security events: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve security events',
            'error': str(e)
        }), 500


@security_bp.route('/api/security/api-keys', methods=['GET'])
@jwt_required()
def get_api_keys():
    """Get user's API keys"""
    try:
        user_id = get_jwt_identity()
        
        api_keys = APIKey.query.filter_by(user_id=user_id).order_by(
            desc(APIKey.created_at)
        ).all()
        
        keys_data = [key.to_dict() for key in api_keys]
        
        return jsonify({
            'success': True,
            'message': 'API keys retrieved successfully',
            'data': {
                'api_keys': keys_data,
                'total': len(keys_data)
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting API keys: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve API keys',
            'error': str(e)
        }), 500


@security_bp.route('/api/security/api-keys', methods=['POST'])
@jwt_required()
def create_api_key():
    """Create a new API key"""
    try:
        user_id = get_jwt_identity()
        
        # Validate request data
        schema = APIKeyCreateSchema()
        try:
            data = schema.load(request.json)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Invalid request data',
                'errors': e.messages
            }), 400
        
        # Generate API key
        api_key, key_hash, key_prefix = APIKey.generate_api_key()
        
        # Calculate expiration date
        expires_at = None
        if data.get('expires_in_days'):
            expires_at = datetime.utcnow() + timedelta(days=data['expires_in_days'])
        
        # Create API key record
        new_api_key = APIKey(
            user_id=user_id,
            key_name=data['key_name'],
            key_description=data['key_description'],
            key_hash=key_hash,
            key_prefix=key_prefix,
            scopes=data['scopes'],
            allowed_endpoints=data['allowed_endpoints'],
            rate_limit_per_hour=data['rate_limit_per_hour'],
            rate_limit_per_day=data['rate_limit_per_day'],
            expires_at=expires_at,
            allowed_ips=data['allowed_ips']
        )
        
        db.session.add(new_api_key)
        db.session.commit()
        
        # Log audit event
        log_audit_event(
            'api_key_created',
            'api_key',
            str(new_api_key.id),
            new_values=new_api_key.to_dict()
        )
        
        # Return API key (only time the full key is shown)
        response_data = new_api_key.to_dict()
        response_data['api_key'] = api_key  # Include full key in response
        
        return jsonify({
            'success': True,
            'message': 'API key created successfully',
            'data': response_data,
            'warning': 'This is the only time the full API key will be shown. Please save it securely.'
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating API key: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Failed to create API key',
            'error': str(e)
        }), 500


@security_bp.route('/api/security/api-keys/<key_id>', methods=['PUT'])
@jwt_required()
def update_api_key(key_id: str):
    """Update API key status or configuration"""
    try:
        user_id = get_jwt_identity()
        
        # Get API key
        api_key = APIKey.query.filter_by(id=key_id, user_id=user_id).first()
        
        if not api_key:
            return jsonify({
                'success': False,
                'message': 'API key not found'
            }), 404
        
        data = request.get_json() or {}
        old_values = api_key.to_dict()
        
        # Update allowed fields
        if 'status' in data:
            try:
                api_key.status = APIKeyStatus(data['status'])
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': f"Invalid status: {data['status']}"
                }), 400
        
        if 'key_name' in data:
            api_key.key_name = data['key_name']
        
        if 'key_description' in data:
            api_key.key_description = data['key_description']
        
        db.session.commit()
        
        # Log audit event
        log_audit_event(
            'api_key_updated',
            'api_key',
            str(api_key.id),
            old_values=old_values,
            new_values=api_key.to_dict()
        )
        
        return jsonify({
            'success': True,
            'message': 'API key updated successfully',
            'data': api_key.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating API key: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Failed to update API key',
            'error': str(e)
        }), 500


@security_bp.route('/api/security/rules', methods=['GET'])
@jwt_required()
def get_security_rules():
    """Get security rules configuration"""
    try:
        # Check admin access
        if not check_admin_access():
            return jsonify({
                'success': False,
                'message': 'Admin access required',
                'error': 'insufficient_permissions'
            }), 403
        
        rules = SecurityRule.query.order_by(SecurityRule.priority, SecurityRule.created_at).all()
        
        rules_data = [rule.to_dict() for rule in rules]
        
        return jsonify({
            'success': True,
            'message': 'Security rules retrieved successfully',
            'data': {
                'rules': rules_data,
                'total': len(rules_data)
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting security rules: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to retrieve security rules',
            'error': str(e)
        }), 500


@security_bp.route('/api/security/rules/<rule_id>', methods=['PUT'])
@jwt_required()
def update_security_rule(rule_id: str):
    """Update security rule configuration"""
    try:
        # Check admin access
        if not check_admin_access():
            return jsonify({
                'success': False,
                'message': 'Admin access required',
                'error': 'insufficient_permissions'
            }), 403
        
        # Validate request data
        schema = SecurityRuleUpdateSchema()
        try:
            data = schema.load(request.json)
        except ValidationError as e:
            return jsonify({
                'success': False,
                'message': 'Invalid request data',
                'errors': e.messages
            }), 400
        
        # Get security rule
        rule = SecurityRule.query.get(rule_id)
        
        if not rule:
            return jsonify({
                'success': False,
                'message': 'Security rule not found'
            }), 404
        
        old_values = rule.to_dict()
        
        # Update fields
        for field, value in data.items():
            if value is not None and hasattr(rule, field):
                setattr(rule, field, value)
        
        db.session.commit()
        
        # Log audit event
        log_audit_event(
            'security_rule_updated',
            'security_rule',
            str(rule.id),
            old_values=old_values,
            new_values=rule.to_dict()
        )
        
        return jsonify({
            'success': True,
            'message': 'Security rule updated successfully',
            'data': rule.to_dict()
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating security rule: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': 'Failed to update security rule',
            'error': str(e)
        }), 500


# JWT Protection Demo Routes
@security_bp.route('/api/protected/basic', methods=['GET'])
@protect_basic_route
def protected_basic_route():
    """Basic protected route - requires valid JWT token"""
    user_info = get_current_user_info()

    return jsonify({
        'success': True,
        'message': 'Access granted to basic protected route',
        'user_info': user_info,
        'protection_level': 'basic'
    }), 200


@security_bp.route('/api/protected/premium', methods=['GET'])
@protect_premium_route
def protected_premium_route():
    """Premium protected route - requires premium subscription"""
    user_info = get_current_user_info()

    return jsonify({
        'success': True,
        'message': 'Access granted to premium protected route',
        'user_info': user_info,
        'protection_level': 'premium',
        'premium_features': [
            'AI-powered suggestions',
            'Advanced analytics',
            'Priority support',
            'Export capabilities'
        ]
    }), 200


@security_bp.route('/api/protected/admin', methods=['GET'])
@protect_admin_route
def protected_admin_route():
    """Admin protected route - requires admin privileges"""
    user_info = get_current_user_info()

    return jsonify({
        'success': True,
        'message': 'Access granted to admin protected route',
        'user_info': user_info,
        'protection_level': 'admin',
        'admin_features': [
            'User management',
            'Security monitoring',
            'System configuration',
            'Analytics dashboard'
        ]
    }), 200


@security_bp.route('/api/auth/check-token', methods=['GET'])
def check_auth_token():
    """Check if current token is valid and return user info"""
    token_info = check_token_validity()

    return jsonify({
        'success': True,
        'message': 'Token validation completed',
        'data': token_info
    }), 200


@security_bp.route('/api/auth/user-info', methods=['GET'])
@jwt_required_with_protection()
def get_user_info():
    """Get current authenticated user information"""
    user_info = get_current_user_info()

    return jsonify({
        'success': True,
        'message': 'User information retrieved successfully',
        'data': user_info
    }), 200
