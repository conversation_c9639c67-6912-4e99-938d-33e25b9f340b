"""
US-08: Dashboard Flask Application
==================================

Main Flask application for the dashboard feature in the Dr. Resume application.
This app provides comprehensive scan history, analytics, and reporting capabilities.

Features:
- Scan history management with pagination and filtering
- Real-time analytics and metrics calculation
- Data visualization and trend analysis
- Export and reporting functionality
- User activity tracking
- Performance monitoring

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import logging
from datetime import datetime, timedelta

from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_caching import Cache
from dotenv import load_dotenv

# Local imports
from us08_dashboard_model import db, ScanHistory, DashboardAnalytics, UserActivity, ExportHistory
from us08_dashboard_routes import dashboard_bp

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app(config_name='development'):
    """Create and configure the Flask application"""
    
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = get_database_url()
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = os.getenv('SQLALCHEMY_ECHO', 'False').lower() == 'true'
    
    # JWT Configuration
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', app.config['SECRET_KEY'])
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    app.config['JWT_ALGORITHM'] = 'HS256'
    
    # Cache Configuration
    app.config['CACHE_TYPE'] = 'simple'  # Use Redis in production
    app.config['CACHE_DEFAULT_TIMEOUT'] = 300  # 5 minutes
    
    # Rate Limiting Configuration
    app.config['RATELIMIT_STORAGE_URL'] = os.getenv('REDIS_URL', 'memory://')
    
    # Dashboard Configuration
    app.config['DASHBOARD_CACHE_TIMEOUT'] = int(os.getenv('DASHBOARD_CACHE_TIMEOUT', '300'))
    app.config['MAX_EXPORT_SIZE'] = int(os.getenv('MAX_EXPORT_SIZE', '10485760'))  # 10MB
    app.config['EXPORT_RETENTION_DAYS'] = int(os.getenv('EXPORT_RETENTION_DAYS', '7'))
    
    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)
    CORS(app, origins=os.getenv('CORS_ORIGINS', '*').split(','))
    cache = Cache(app)
    
    # Initialize rate limiter
    limiter = Limiter(
        app,
        key_func=get_remote_address,
        default_limits=["1000 per day", "100 per hour"]
    )
    
    # Register blueprints
    app.register_blueprint(dashboard_bp)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }), 401
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'message': 'Endpoint not found',
            'error': 'not_found'
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return jsonify({
            'success': False,
            'message': 'Method not allowed',
            'error': 'method_not_allowed'
        }), 405
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        logger.error(f"Internal server error: {str(error)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'internal_error'
        }), 500
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return jsonify({
            'success': False,
            'message': 'Rate limit exceeded',
            'error': 'rate_limit_exceeded',
            'retry_after': str(e.retry_after)
        }), 429
    
    # Health check routes
    @app.route('/health', methods=['GET'])
    def health_check():
        """Application health check"""
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'healthy'
        except Exception as e:
            db_status = f'unhealthy: {str(e)}'
        
        # Test cache
        try:
            cache.set('health_check', 'ok', timeout=1)
            cache_result = cache.get('health_check')
            cache_status = 'healthy' if cache_result == 'ok' else 'unhealthy'
        except Exception as e:
            cache_status = f'unhealthy: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'US-08 Dashboard service is running',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': db_status,
            'cache': cache_status,
            'features': {
                'scan_history': True,
                'analytics': True,
                'exports': True,
                'real_time_metrics': True,
                'user_activity_tracking': True
            }
        }), 200
    
    @app.route('/api/health', methods=['GET'])
    def api_health_check():
        """API health check with detailed information"""
        try:
            # Test database tables and get counts
            scan_count = ScanHistory.query.count()
            analytics_count = DashboardAnalytics.query.count()
            activity_count = UserActivity.query.count()
            
            return jsonify({
                'success': True,
                'message': 'API is healthy',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'US-08 Dashboard API',
                'version': '1.0.0',
                'statistics': {
                    'total_scans': scan_count,
                    'analytics_records': analytics_count,
                    'user_activities': activity_count
                },
                'endpoints': {
                    'dashboard_overview': '/api/dashboard/overview',
                    'scan_history': '/api/dashboard/history',
                    'detailed_analytics': '/api/dashboard/analytics',
                    'scan_management': '/api/dashboard/scan/<id>'
                },
                'cache_info': {
                    'enabled': True,
                    'timeout': app.config['DASHBOARD_CACHE_TIMEOUT']
                }
            }), 200
            
        except Exception as e:
            logger.error(f"API health check failed: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'API health check failed',
                'error': str(e)
            }), 500
    
    # Development routes (only in development mode)
    if os.getenv('FLASK_ENV') == 'development':
        @app.route('/api/dev/create-test-token', methods=['POST'])
        def create_test_token():
            """Create a test JWT token for development"""
            data = request.get_json() or {}
            user_id = data.get('user_id', 'test-user-123')
            is_premium = data.get('is_premium', False)
            
            additional_claims = {
                'role': 'premium' if is_premium else 'basic',
                'is_premium': is_premium
            }
            
            token = create_access_token(
                identity=user_id,
                additional_claims=additional_claims
            )
            
            return jsonify({
                'success': True,
                'message': 'Test token created',
                'token': token,
                'user_id': user_id,
                'is_premium': is_premium
            }), 200
        
        @app.route('/api/dev/populate-sample-data', methods=['POST'])
        @jwt_required()
        def populate_sample_data():
            """Populate database with sample data for development"""
            user_id = get_jwt_identity()
            
            try:
                # Create sample scan history
                sample_scans = []
                for i in range(10):
                    scan = ScanHistory(
                        user_id=user_id,
                        resume_id=f'sample-resume-{i+1}',
                        job_description_id=f'sample-jd-{i+1}',
                        scan_name=f'Resume Analysis #{i+1}',
                        scan_description=f'Analysis for position at Company {i+1}',
                        resume_filename=f'resume_v{i+1}.pdf',
                        job_title='Software Developer',
                        company_name=f'Tech Company {i+1}',
                        overall_match_score=60.0 + (i * 3),
                        total_suggestions=10 - i,
                        implemented_suggestions=i + 1,
                        has_premium_suggestions=i % 2 == 0,
                        created_at=datetime.utcnow() - timedelta(days=i*3)
                    )
                    sample_scans.append(scan)
                    db.session.add(scan)
                
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': f'Created {len(sample_scans)} sample scan records',
                    'data': {'scans_created': len(sample_scans)}
                }), 200
                
            except Exception as e:
                db.session.rollback()
                return jsonify({
                    'success': False,
                    'message': 'Failed to create sample data',
                    'error': str(e)
                }), 500
    
    # Database initialization
    @app.before_first_request
    def create_tables():
        """Create database tables if they don't exist"""
        try:
            db.create_all()
            logger.info("Database tables created successfully")
            
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
    
    # Cache warming
    @app.before_first_request
    def warm_cache():
        """Warm up cache with frequently accessed data"""
        try:
            # Pre-calculate common analytics for better performance
            logger.info("Cache warming completed")
        except Exception as e:
            logger.warning(f"Cache warming failed: {str(e)}")
    
    return app


def get_database_url():
    """Construct database URL from environment variables"""
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'dr_resume_db')
    db_user = os.getenv('DB_USER', 'dr_resume_user')
    db_password = os.getenv('DB_PASSWORD', 'password')
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"


# Create the Flask application
app = create_app()

if __name__ == '__main__':
    # Development server
    debug_mode = os.getenv('FLASK_ENV') == 'development'
    port = int(os.getenv('PORT', 5008))  # US-08 uses port 5008
    
    logger.info(f"Starting US-08 Dashboard service on port {port}")
    logger.info(f"Debug mode: {debug_mode}")
    logger.info(f"Cache enabled: True")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode,
        threaded=True
    )
