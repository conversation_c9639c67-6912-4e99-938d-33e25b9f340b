<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Settings - Dr. Resume</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .settings-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .settings-section {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 2rem;
            margin-bottom: 2rem;
        }

        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #ced4da;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-save {
            background: linear-gradient(45deg, var(--success-color), #2ecc71);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-save:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            color: white;
        }

        .btn-danger-action {
            background: linear-gradient(45deg, var(--danger-color), #ec7063);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-danger-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            color: white;
        }

        .password-strength {
            margin-top: 0.5rem;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .strength-weak { background-color: #e74c3c; width: 25%; }
        .strength-fair { background-color: #f39c12; width: 50%; }
        .strength-good { background-color: #f1c40f; width: 75%; }
        .strength-strong { background-color: #27ae60; width: 100%; }

        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--secondary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-cog me-2"></i>
                Dr. Resume - Account Settings
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../US-08-Dashboard/frontend/us08_dashboard.html">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="#" id="logoutBtn">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-6 fw-bold text-dark">
                    <i class="fas fa-user-cog text-primary me-3"></i>
                    Account Settings
                </h1>
                <p class="lead text-muted">Manage your profile, security, and preferences</p>
            </div>
        </div>

        <!-- Alert Messages -->
        <div id="alertContainer"></div>

        <!-- Profile Information -->
        <div class="settings-card">
            <div class="settings-section">
                <h4 class="section-title">
                    <i class="fas fa-user"></i>
                    Profile Information
                </h4>
                
                <form id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="firstName" class="form-label">First Name</label>
                            <input type="text" class="form-control" id="firstName" name="first_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="lastName" class="form-label">Last Name</label>
                            <input type="text" class="form-control" id="lastName" name="last_name">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="displayName" class="form-label">Display Name</label>
                            <input type="text" class="form-control" id="displayName" name="display_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" readonly>
                            <small class="text-muted">Use "Update Account" section to change email</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bio" class="form-label">Bio</label>
                        <textarea class="form-control" id="bio" name="bio" rows="3" placeholder="Tell us about yourself..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="jobTitle" class="form-label">Job Title</label>
                            <input type="text" class="form-control" id="jobTitle" name="job_title">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="company" class="form-label">Company</label>
                            <input type="text" class="form-control" id="company" name="company">
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>Save Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Account Security -->
            <div class="settings-section">
                <h4 class="section-title">
                    <i class="fas fa-shield-alt"></i>
                    Account Security
                </h4>
                
                <!-- Update Email/Password -->
                <div class="mb-4">
                    <h6 class="fw-bold mb-3">Update Account Credentials</h6>
                    <form id="accountUpdateForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="newEmail" class="form-label">New Email Address</label>
                                <input type="email" class="form-control" id="newEmail" name="email">
                                <small class="text-muted">Leave blank to keep current email</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="currentPasswordForAccount" class="form-label">Current Password *</label>
                                <input type="password" class="form-control" id="currentPasswordForAccount" name="current_password" required>
                                <small class="text-muted">Required for security verification</small>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Update Account
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Change Password -->
                <div class="mb-4">
                    <h6 class="fw-bold mb-3">Change Password</h6>
                    <form id="passwordChangeForm">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="currentPassword" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="newPassword" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="newPassword" name="new_password" required>
                                <div class="password-strength mt-2">
                                    <div class="strength-bar" id="strengthBar"></div>
                                    <small class="text-muted" id="strengthText">Enter a password</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                                <small class="text-muted" id="passwordMatch"></small>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-danger-action">
                                <i class="fas fa-key me-2"></i>Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Preferences -->
            <div class="settings-section">
                <h4 class="section-title">
                    <i class="fas fa-cog"></i>
                    Preferences
                </h4>
                
                <form id="preferencesForm">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Notifications</h6>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="form-label mb-0">Email Notifications</label>
                                <label class="switch">
                                    <input type="checkbox" id="emailNotifications" name="email_notifications">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="form-label mb-0">Marketing Emails</label>
                                <label class="switch">
                                    <input type="checkbox" id="marketingEmails" name="marketing_emails">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="form-label mb-0">Security Alerts</label>
                                <label class="switch">
                                    <input type="checkbox" id="securityAlerts" name="security_alerts">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Application</h6>
                            
                            <div class="mb-3">
                                <label for="theme" class="form-label">Theme</label>
                                <select class="form-select" id="theme" name="theme">
                                    <option value="light">Light</option>
                                    <option value="dark">Dark</option>
                                    <option value="auto">Auto</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="itemsPerPage" class="form-label">Items Per Page</label>
                                <select class="form-select" id="itemsPerPage" name="items_per_page">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="form-label mb-0">Auto Save</label>
                                <label class="switch">
                                    <input type="checkbox" id="autoSave" name="auto_save">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>Save Preferences
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 mb-0">Saving changes...</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us10_account.js"></script>
</body>
</html>
