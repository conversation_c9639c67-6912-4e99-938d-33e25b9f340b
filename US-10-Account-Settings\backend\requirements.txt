# US-10: Account Settings - Python Dependencies
# ==============================================
# 
# This file contains all Python packages required for US-10 Account Settings feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01 through US-09, plus new account management requirements

# Core Flask Framework (from previous US)
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (from previous US)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration (from previous US)
Flask-CORS==4.0.0

# Environment Variables Management (from previous US)
python-dotenv==1.0.0

# Date and Time Utilities (from previous US)
python-dateutil==2.8.2

# JSON Handling and Validation (from previous US)
jsonschema==4.19.1

# NEW: Account Management Libraries for US-10
# ===========================================

# Email Validation and Verification
email-validator==2.0.0
dns==1.16.0

# Email Sending for Account Verification
Flask-Mail==0.9.1
sendgrid==6.10.0

# Image Processing for Profile Pictures
Pillow==10.0.1
python-magic==0.4.27

# File Upload Handling
Flask-Uploads==0.2.1

# Form Handling and Validation
Flask-WTF==1.2.1
WTForms==3.1.0

# Password Strength Validation
password-strength==0.0.3

# Two-Factor Authentication
pyotp==2.9.0
qrcode[pil]==7.4.2

# Phone Number Validation
phonenumbers==8.13.19

# Country and Timezone Data
pycountry==22.3.5
pytz==2023.3

# Subscription and Payment Processing
stripe==6.6.0

# Data Export Functionality
openpyxl==3.1.2
xlsxwriter==3.1.9
reportlab==4.0.4

# Account Activity Tracking
user-agents==2.2.0

# Session Management
Flask-Session==0.5.0

# Account Security
cryptography==41.0.4
secrets==1.0.0

# Rate Limiting for Account Operations
Flask-Limiter==3.5.0
redis==5.0.1

# Input Sanitization
bleach==6.1.0

# Notification System
Flask-SocketIO==5.3.6
python-socketio==5.9.0

# Background Tasks for Account Operations
celery==5.3.4
kombu==5.3.4

# Account Analytics
pandas==2.0.3
numpy==1.24.3

# Configuration Management
configparser==6.0.0

# Logging and Monitoring
structlog==23.2.0
colorlog==6.7.0

# API Documentation
flask-restx==1.2.0
apispec==6.3.0

# Request/Response Validation
marshmallow==3.20.1
marshmallow-sqlalchemy==0.29.0

# Security Headers
Flask-Talisman==1.1.0

# GDPR Compliance and Data Privacy
anonymizedf==1.0.0

# Account Backup and Recovery
boto3==1.28.85  # For AWS S3 backup storage

# Multi-language Support
Flask-Babel==4.0.0
Babel==2.12.1

# Account Preferences Storage
jsonfield==3.1.0

# Social Media Integration
python-social-auth==0.3.6
social-auth-app-flask==1.0.0

# Account Verification via SMS
twilio==8.10.0

# Account Import/Export
python-csv==0.0.13

# Account Deactivation and Deletion
schedule==1.2.0

# Account Migration Tools
alembic==1.12.0
Flask-Migrate==4.0.5

# Account Statistics
matplotlib==3.7.2
seaborn==0.12.2

# Account Health Monitoring
healthcheck==1.3.3

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
pytest-mock==3.12.0
responses==0.23.3
factory-boy==3.3.0

# Production Server
gunicorn==21.2.0

# Built-in Python modules (listed for clarity):
# hashlib - Secure hash algorithms (built-in)
# hmac - Keyed-Hashing for Message Authentication (built-in)
# secrets - Generate secure random numbers (built-in)
# uuid - UUID generation (built-in)
# datetime - Date and time (built-in)
# json - JSON encoder/decoder (built-in)
# re - Regular expressions (built-in)
# os - Operating system interface (built-in)
# sys - System-specific parameters (built-in)
# logging - Logging facility (built-in)
# base64 - Base64 encoding/decoding (built-in)
# urllib - URL handling modules (built-in)
# mimetypes - Map filenames to MIME types (built-in)
# tempfile - Generate temporary files and directories (built-in)
# shutil - High-level file operations (built-in)
# zipfile - Work with ZIP archives (built-in)
# csv - CSV file reading and writing (built-in)
# io - Core tools for working with streams (built-in)
