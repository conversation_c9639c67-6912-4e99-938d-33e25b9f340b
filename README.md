# Dr. Resume - AI Resume Scanner 🏥📄

## 🌟 Project Overview

**Dr. Resume** is a comprehensive AI-powered resume scanning and optimization platform that helps job seekers improve their resumes by analyzing them against job descriptions. The application uses advanced Natural Language Processing (NLP), machine learning algorithms, and OpenAI integration to provide intelligent suggestions and matching scores.

### 🎯 What Dr. Resume Does

- **📤 Resume Upload**: Secure file upload with support for PDF and DOCX formats
- **📋 Job Description Analysis**: Intelligent parsing and keyword extraction
- **🔍 Smart Matching**: Advanced algorithms calculate compatibility scores
- **💡 AI Suggestions**: OpenAI-powered recommendations for resume improvement
- **📊 Analytics Dashboard**: Comprehensive tracking and progress monitoring
- **🔒 Enterprise Security**: Advanced API protection and threat detection
- **👤 Account Management**: Complete user profile and subscription management

## 🏗️ Architecture Overview

The application is built using a **microservices architecture** with 10 distinct user stories (US), each handling specific functionality:

```
Dr. Resume Application
├── US-01: User Registration & Authentication
├── US-02: Enhanced Authentication (JWT + Security)
├── US-03: Resume Upload & Processing
├── US-04: Job Description Management
├── US-05: NLP Processing & Keyword Extraction
├── US-06: Matching Score Calculation
├── US-07: Suggestions (Basic + Premium OpenAI)
├── US-08: Dashboard & Scan History
├── US-09: API Protection & Security
└── US-10: Account Settings & Management
```

## 🚀 Quick Start Guide

### Prerequisites

- **Python 3.9+**
- **PostgreSQL 13+**
- **Redis** (for caching and rate limiting)
- **OpenAI API Key** (for premium features)
- **Node.js** (for frontend development tools)

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/dr-resume.git
cd dr-resume
```

### 2. Database Setup

```bash
# Start PostgreSQL service
sudo systemctl start postgresql

# Create database and user
sudo -u postgres psql
CREATE DATABASE dr_resume_db;
CREATE USER dr_resume_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE dr_resume_db TO dr_resume_user;
\q
```

### 3. Environment Configuration

Create a `.env` file in the root directory:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=dr_resume_db
DB_USER=dr_resume_user
DB_PASSWORD=your_secure_password

# Security Configuration
SECRET_KEY=your-super-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key

# OpenAI Configuration (for premium features)
OPENAI_API_KEY=your-openai-api-key-here

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Payment Configuration (for subscriptions)
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# Redis Configuration
REDIS_URL=redis://localhost:6379

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

### 4. Install and Run Each Service

Each US can be run independently. Here's how to start the core services:

```bash
# US-01: User Registration (Port 5001)
cd US-01-User-Registration/backend
python -m venv venv && source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
python us01_app.py

# US-03: Resume Upload (Port 5003)
cd US-03-Resume-Upload/backend
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt
python us03_app.py

# US-06: Matching Score (Port 5006)
cd US-06-Matching-Score/backend
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt
python us06_app.py

# US-08: Dashboard (Port 5008)
cd US-08-Dashboard/backend
python -m venv venv && source venv/bin/activate
pip install -r requirements.txt
python us08_app.py
```

### 5. Access the Application

- **Frontend**: `http://localhost:3000`
- **API Documentation**: `http://localhost:500X/api/health` (where X is the US number)
- **Health Checks**: `http://localhost:500X/health`

## 📋 User Stories Breakdown

| US | Feature | Port | Description |
|----|---------|------|-------------|
| **US-01** | User Registration | 5001 | User signup, login, profile management |
| **US-02** | Enhanced Auth | 5002 | JWT tokens, password reset, email verification |
| **US-03** | Resume Upload | 5003 | File upload, PDF/DOCX processing, storage |
| **US-04** | Job Description | 5004 | Job posting management, requirement parsing |
| **US-05** | NLP Processing | 5005 | Keyword extraction, text analysis, similarity |
| **US-06** | Matching Score | 5006 | Algorithm-based compatibility scoring |
| **US-07** | Suggestions | 5007 | Basic + AI-powered improvement recommendations |
| **US-08** | Dashboard | 5008 | Analytics, history, progress tracking |
| **US-09** | API Protection | 5009 | Security, rate limiting, threat detection |
| **US-10** | Account Settings | 5010 | Profile, subscriptions, privacy settings |

## 🛠️ Technology Stack

### Backend
- **Framework**: Flask (Python)
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT with Flask-JWT-Extended
- **Caching**: Redis
- **File Processing**: PyPDF2, python-docx
- **NLP**: spaCy, NLTK, scikit-learn
- **AI Integration**: OpenAI API
- **Security**: Flask-Limiter, bcrypt, input validation

### Frontend
- **Core**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Bootstrap 5
- **Charts**: Chart.js
- **Icons**: Font Awesome
- **HTTP Client**: Fetch API

### Infrastructure
- **Database**: PostgreSQL 13+
- **Cache**: Redis
- **File Storage**: Local filesystem (configurable for cloud)
- **Email**: SMTP/SendGrid
- **Payments**: Stripe
- **Deployment**: Docker-ready, Gunicorn for production

## 🔒 Security Features

- **🛡️ Input Validation**: Comprehensive sanitization and validation
- **🚫 Rate Limiting**: Intelligent throttling and abuse prevention
- **🔐 Authentication**: JWT with refresh tokens and secure sessions
- **🕵️ Threat Detection**: Real-time security monitoring and blocking
- **📊 Audit Logging**: Complete activity tracking and compliance
- **🔒 Data Encryption**: Secure password hashing and sensitive data protection

## 📊 Key Features

### For Job Seekers
- **Resume Analysis**: Get detailed feedback on resume content and structure
- **Job Matching**: See how well your resume matches specific job descriptions
- **AI Suggestions**: Receive intelligent recommendations for improvement
- **Progress Tracking**: Monitor your optimization journey over time
- **Export Options**: Download reports and analysis results

### For Developers
- **Microservices Architecture**: Scalable and maintainable codebase
- **Comprehensive APIs**: RESTful endpoints with OpenAPI documentation
- **Security First**: Enterprise-grade security and monitoring
- **Extensible Design**: Easy to add new features and integrations
- **Test Coverage**: Comprehensive test suites for all components

## 🧪 Testing

Each US includes comprehensive test suites:

```bash
# Run tests for a specific US
cd US-XX-Feature-Name/tests
pytest test_usXX_feature.py -v

# Run all tests with coverage
pytest --cov=backend --cov-report=html
```

## 📚 Documentation

Each user story includes detailed documentation:

- **README_USXX.md**: Complete implementation guide
- **API Documentation**: OpenAPI/Swagger specs
- **Database Schema**: ERD and table definitions
- **Security Guidelines**: Best practices and configurations

## 🚀 Deployment

### Development
```bash
# Each service runs independently
python usXX_app.py
```

### Production
```bash
# Using Gunicorn
gunicorn -w 4 -b 0.0.0.0:5001 us01_app:app

# Using Docker (if Dockerfile is created)
docker build -t dr-resume-us01 .
docker run -p 5001:5001 dr-resume-us01
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for providing powerful AI capabilities
- **spaCy** and **NLTK** for NLP processing
- **Flask** community for the excellent web framework
- **Bootstrap** for responsive UI components
- **Chart.js** for beautiful data visualizations

## 📞 Support

For support, email <EMAIL> or create an issue in this repository.

---

**🎉 Dr. Resume - Making job applications smarter, one resume at a time!**
