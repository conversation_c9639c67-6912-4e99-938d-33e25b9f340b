"""
US-07: Suggestions Flask Application
====================================

Main Flask application for the suggestions feature in the Dr. Resume application.
This app provides both basic keyword suggestions and premium AI-powered suggestions.

Features:
- Basic keyword suggestions using local algorithms
- Premium AI suggestions using OpenAI API
- JWT-based authentication and authorization
- Role-based access control for premium features
- Comprehensive error handling and logging

Author: Dr. <PERSON>sume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import logging
from datetime import datetime, timedelta

from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from dotenv import load_dotenv

# Local imports
from us07_suggestions_model import db, Suggestion, PremiumSuggestion, SuggestionCategory
from us07_suggestions_routes import suggestions_bp

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app(config_name='development'):
    """Create and configure the Flask application"""
    
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = get_database_url()
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = os.getenv('SQLALCHEMY_ECHO', 'False').lower() == 'true'
    
    # JWT Configuration
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', app.config['SECRET_KEY'])
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    app.config['JWT_ALGORITHM'] = 'HS256'
    
    # OpenAI Configuration
    app.config['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY')
    app.config['OPENAI_MODEL'] = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
    app.config['OPENAI_MAX_TOKENS'] = int(os.getenv('OPENAI_MAX_TOKENS', '2000'))
    
    # Rate Limiting Configuration
    app.config['RATELIMIT_STORAGE_URL'] = os.getenv('REDIS_URL', 'memory://')
    
    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)
    CORS(app, origins=os.getenv('CORS_ORIGINS', '*').split(','))
    
    # Initialize rate limiter
    limiter = Limiter(
        app,
        key_func=get_remote_address,
        default_limits=["200 per day", "50 per hour"]
    )
    
    # Register blueprints
    app.register_blueprint(suggestions_bp)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }), 401
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'message': 'Endpoint not found',
            'error': 'not_found'
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return jsonify({
            'success': False,
            'message': 'Method not allowed',
            'error': 'method_not_allowed'
        }), 405
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        logger.error(f"Internal server error: {str(error)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'internal_error'
        }), 500
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return jsonify({
            'success': False,
            'message': 'Rate limit exceeded',
            'error': 'rate_limit_exceeded',
            'retry_after': str(e.retry_after)
        }), 429
    
    # Health check routes
    @app.route('/health', methods=['GET'])
    def health_check():
        """Application health check"""
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'healthy'
        except Exception as e:
            db_status = f'unhealthy: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'US-07 Suggestions service is running',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': db_status,
            'features': {
                'basic_suggestions': True,
                'premium_suggestions': bool(os.getenv('OPENAI_API_KEY')),
                'rate_limiting': True,
                'jwt_auth': True
            }
        }), 200
    
    @app.route('/api/health', methods=['GET'])
    def api_health_check():
        """API health check with more detailed information"""
        try:
            # Test database tables
            suggestion_count = Suggestion.query.count()
            premium_count = PremiumSuggestion.query.count()
            
            return jsonify({
                'success': True,
                'message': 'API is healthy',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'US-07 Suggestions API',
                'version': '1.0.0',
                'statistics': {
                    'total_suggestions': suggestion_count,
                    'premium_suggestions': premium_count
                },
                'endpoints': {
                    'basic_suggestions': '/api/suggestions/<resume_id>/<jd_id>',
                    'premium_suggestions': '/api/premium-suggestions',
                    'suggestion_status': '/api/suggestions/<id>/status'
                }
            }), 200
            
        except Exception as e:
            logger.error(f"API health check failed: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'API health check failed',
                'error': str(e)
            }), 500
    
    # Development routes (only in development mode)
    if os.getenv('FLASK_ENV') == 'development':
        @app.route('/api/dev/create-test-token', methods=['POST'])
        def create_test_token():
            """Create a test JWT token for development"""
            data = request.get_json() or {}
            user_id = data.get('user_id', 'test-user-123')
            is_premium = data.get('is_premium', False)
            
            additional_claims = {
                'role': 'premium' if is_premium else 'basic',
                'is_premium': is_premium
            }
            
            token = create_access_token(
                identity=user_id,
                additional_claims=additional_claims
            )
            
            return jsonify({
                'success': True,
                'message': 'Test token created',
                'token': token,
                'user_id': user_id,
                'is_premium': is_premium
            }), 200
    
    # Database initialization
    @app.before_first_request
    def create_tables():
        """Create database tables if they don't exist"""
        try:
            db.create_all()
            logger.info("Database tables created successfully")
            
            # Create default suggestion categories
            create_default_categories()
            
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
    
    return app


def get_database_url():
    """Construct database URL from environment variables"""
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'dr_resume_db')
    db_user = os.getenv('DB_USER', 'dr_resume_user')
    db_password = os.getenv('DB_PASSWORD', 'password')
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"


def create_default_categories():
    """Create default suggestion categories"""
    default_categories = [
        {
            'name': 'Missing Keywords',
            'description': 'Keywords that appear in job description but missing from resume',
            'icon': 'fas fa-search',
            'color': '#e74c3c',
            'sort_order': 1
        },
        {
            'name': 'Skills Enhancement',
            'description': 'Suggestions for improving existing skills presentation',
            'icon': 'fas fa-cogs',
            'color': '#3498db',
            'sort_order': 2
        },
        {
            'name': 'Experience Optimization',
            'description': 'Ways to better present work experience and achievements',
            'icon': 'fas fa-briefcase',
            'color': '#2ecc71',
            'sort_order': 3
        },
        {
            'name': 'Format Improvements',
            'description': 'Resume formatting and structure suggestions',
            'icon': 'fas fa-file-alt',
            'color': '#f39c12',
            'sort_order': 4
        }
    ]
    
    for cat_data in default_categories:
        existing = SuggestionCategory.query.filter_by(name=cat_data['name']).first()
        if not existing:
            category = SuggestionCategory(**cat_data)
            db.session.add(category)
    
    try:
        db.session.commit()
        logger.info("Default suggestion categories created")
    except Exception as e:
        logger.error(f"Error creating default categories: {str(e)}")
        db.session.rollback()


# Create the Flask application
app = create_app()

if __name__ == '__main__':
    # Development server
    debug_mode = os.getenv('FLASK_ENV') == 'development'
    port = int(os.getenv('PORT', 5007))  # US-07 uses port 5007
    
    logger.info(f"Starting US-07 Suggestions service on port {port}")
    logger.info(f"Debug mode: {debug_mode}")
    logger.info(f"OpenAI API configured: {bool(os.getenv('OPENAI_API_KEY'))}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode,
        threaded=True
    )
