<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Protection Demo - Dr. Resume</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .protection-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .protection-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .auth-status {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border: 2px solid;
        }

        .auth-status.authenticated {
            background-color: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }

        .auth-status.unauthenticated {
            background-color: #f8d7da;
            border-color: #e74c3c;
            color: #721c24;
        }

        .auth-status.checking {
            background-color: #fff3cd;
            border-color: #f39c12;
            color: #856404;
        }

        .protection-level {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .level-basic {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .level-premium {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .level-admin {
            background-color: #fce4ec;
            color: #c2185b;
        }

        .test-button {
            margin: 0.5rem;
            min-width: 150px;
        }

        .response-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .loading-spinner {
            display: none;
        }

        .premium-required {
            background: linear-gradient(45deg, #ff6b35, #f7931e);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                Dr. Resume - API Protection
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" id="loginBtn">
                    <i class="fas fa-sign-in-alt me-1"></i>Login
                </a>
                <a class="nav-link hidden" href="#" id="logoutBtn">
                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Authentication Status -->
        <div class="auth-status checking" id="authStatus">
            <div class="d-flex align-items-center">
                <div class="loading-spinner spinner-border spinner-border-sm me-3" id="authSpinner">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div>
                    <h5 class="mb-1" id="authStatusTitle">Checking Authentication...</h5>
                    <p class="mb-0" id="authStatusMessage">Verifying your access token...</p>
                </div>
            </div>
        </div>

        <!-- User Information -->
        <div class="protection-card hidden" id="userInfoCard">
            <h4><i class="fas fa-user me-2"></i>User Information</h4>
            <div id="userInfoContent"></div>
        </div>

        <!-- API Protection Demo -->
        <div class="protection-card">
            <h3><i class="fas fa-shield-alt me-2"></i>API Protection Demonstration</h3>
            <p class="text-muted">Test different protection levels and see how JWT authentication and role-based access control work.</p>

            <!-- Basic Protection -->
            <div class="mb-4">
                <div class="protection-level level-basic">
                    <i class="fas fa-lock me-2"></i>Basic Protection
                </div>
                <h5>JWT Authentication Required</h5>
                <p>This endpoint requires a valid JWT token but no specific role.</p>
                <button class="btn btn-primary test-button" onclick="testBasicProtection()">
                    <i class="fas fa-key me-2"></i>Test Basic Route
                </button>
                <div class="response-area hidden" id="basicResponse"></div>
            </div>

            <!-- Premium Protection -->
            <div class="mb-4">
                <div class="protection-level level-premium">
                    <i class="fas fa-crown me-2"></i>Premium Protection
                </div>
                <h5>Premium Subscription Required</h5>
                <p>This endpoint requires a valid JWT token with premium role or subscription.</p>
                <button class="btn btn-warning test-button" onclick="testPremiumProtection()">
                    <i class="fas fa-star me-2"></i>Test Premium Route
                </button>
                <div class="premium-required hidden" id="premiumUpgrade">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Premium Access Required</h6>
                    <p class="mb-2">This feature requires a premium subscription. Upgrade now to access advanced features!</p>
                    <button class="btn btn-light btn-sm" onclick="upgradeAccount()">
                        <i class="fas fa-arrow-up me-2"></i>Upgrade to Premium
                    </button>
                </div>
                <div class="response-area hidden" id="premiumResponse"></div>
            </div>

            <!-- Admin Protection -->
            <div class="mb-4">
                <div class="protection-level level-admin">
                    <i class="fas fa-user-shield me-2"></i>Admin Protection
                </div>
                <h5>Administrator Access Required</h5>
                <p>This endpoint requires a valid JWT token with admin role.</p>
                <button class="btn btn-danger test-button" onclick="testAdminProtection()">
                    <i class="fas fa-cog me-2"></i>Test Admin Route
                </button>
                <div class="response-area hidden" id="adminResponse"></div>
            </div>
        </div>

        <!-- Token Management -->
        <div class="protection-card">
            <h4><i class="fas fa-key me-2"></i>Token Management</h4>
            <p class="text-muted">Manage your authentication tokens and test token validation.</p>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-info test-button" onclick="checkToken()">
                        <i class="fas fa-search me-2"></i>Check Token
                    </button>
                    <button class="btn btn-success test-button" onclick="refreshToken()">
                        <i class="fas fa-sync me-2"></i>Refresh Token
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-secondary test-button" onclick="getUserInfo()">
                        <i class="fas fa-info me-2"></i>Get User Info
                    </button>
                    <button class="btn btn-outline-danger test-button" onclick="clearToken()">
                        <i class="fas fa-trash me-2"></i>Clear Token
                    </button>
                </div>
            </div>
            
            <div class="response-area hidden" id="tokenResponse"></div>
        </div>

        <!-- Security Dashboard (Admin Only) -->
        <div class="protection-card hidden" id="securityDashboard">
            <h4><i class="fas fa-tachometer-alt me-2"></i>Security Dashboard</h4>
            <p class="text-muted">View security events and system status (Admin access required).</p>
            
            <button class="btn btn-danger test-button" onclick="loadSecurityDashboard()">
                <i class="fas fa-shield-alt me-2"></i>Load Security Dashboard
            </button>
            
            <div class="response-area hidden" id="dashboardResponse"></div>
        </div>
    </div>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Login Required</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>You need to login to access protected features.</p>
                    <div class="mb-3">
                        <label class="form-label">User Type:</label>
                        <select class="form-select" id="userTypeSelect">
                            <option value="basic">Basic User</option>
                            <option value="premium">Premium User</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="performLogin()">Login</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us09_protection.js"></script>
</body>
</html>
