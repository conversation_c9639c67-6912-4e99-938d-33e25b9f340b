/**
 * US-08: Dashboard Frontend JavaScript
 * ====================================
 * 
 * This file handles the frontend functionality for the dashboard feature
 * in the Dr. Resume application. It manages data visualization, scan history,
 * analytics, and user interactions.
 * 
 * Features:
 * - Real-time dashboard metrics and analytics
 * - Interactive charts and data visualization
 * - Scan history management with filtering and pagination
 * - Export functionality for reports
 * - Responsive design and smooth animations
 * 
 * Author: Dr. Resume Development Team
 * Date: 2025-07-23
 * Version: 1.0.0
 */

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://localhost:5008/api',
    ENDPOINTS: {
        DASHBOARD_OVERVIEW: '/dashboard/overview',
        SCAN_HISTORY: '/dashboard/history',
        ANALYTICS: '/dashboard/analytics',
        SCAN_UPDATE: '/dashboard/scan'
    },
    STORAGE_KEYS: {
        AUTH_TOKEN: 'dr_resume_token',
        USER_DATA: 'dr_resume_user'
    },
    CHART_COLORS: {
        primary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        info: '#17a2b8'
    }
};

// Global state
let currentUser = null;
let authToken = null;
let dashboardData = null;
let scanHistory = [];
let currentPage = 1;
let currentFilter = 'all';
let charts = {};

// DOM elements
let elements = {};

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeAuth();
    setupEventListeners();
    loadDashboard();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        loadingSpinner: document.getElementById('loadingSpinner'),
        metricsSection: document.getElementById('metricsSection'),
        scanHistorySection: document.getElementById('scanHistorySection'),
        paginationContainer: document.getElementById('paginationContainer'),
        emptyState: document.getElementById('emptyState'),
        refreshDashboard: document.getElementById('refreshDashboard'),
        exportReportBtn: document.getElementById('exportReportBtn'),
        userProfile: document.getElementById('userProfile'),
        logoutBtn: document.getElementById('logoutBtn'),
        
        // Metric elements
        totalScans: document.getElementById('totalScans'),
        averageScore: document.getElementById('averageScore'),
        implementationRate: document.getElementById('implementationRate'),
        premiumUsage: document.getElementById('premiumUsage'),
        scansChange: document.getElementById('scansChange'),
        scoreChange: document.getElementById('scoreChange'),
        implementationChange: document.getElementById('implementationChange'),
        premiumChange: document.getElementById('premiumChange'),
        
        // Chart canvases
        scoreChart: document.getElementById('scoreChart'),
        distributionChart: document.getElementById('distributionChart')
    };
}

/**
 * Initialize authentication and user data
 */
function initializeAuth() {
    authToken = localStorage.getItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    const userData = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_DATA);
    
    if (userData) {
        currentUser = JSON.parse(userData);
        updateUserInterface();
    }
    
    if (!authToken) {
        // For demo purposes, create a test token
        createTestToken();
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Refresh dashboard button
    elements.refreshDashboard.addEventListener('click', function() {
        loadDashboard(true);
    });
    
    // Export report button
    elements.exportReportBtn.addEventListener('click', exportReport);
    
    // Filter buttons
    document.querySelectorAll('.btn-filter').forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            setActiveFilter(filter);
            loadScanHistory(1, filter);
        });
    });
    
    // Logout button
    elements.logoutBtn.addEventListener('click', logout);
}

/**
 * Update user interface based on user data
 */
function updateUserInterface() {
    if (currentUser) {
        elements.userProfile.innerHTML = `<i class="fas fa-user me-1"></i>${currentUser.email || 'User'}`;
    }
}

/**
 * Load dashboard data
 */
async function loadDashboard(forceRefresh = false) {
    try {
        showLoading(true);
        hideEmptyState();
        
        // Load dashboard overview
        await loadDashboardOverview();
        
        // Load scan history
        await loadScanHistory();
        
        // Initialize charts
        initializeCharts();
        
    } catch (error) {
        console.error('Error loading dashboard:', error);
        showError('Failed to load dashboard data. Please try again.');
    } finally {
        showLoading(false);
    }
}

/**
 * Load dashboard overview metrics
 */
async function loadDashboardOverview() {
    try {
        const response = await fetch(
            `${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.DASHBOARD_OVERVIEW}?days=30`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const data = await response.json();
        
        if (data.success) {
            dashboardData = data.data;
            updateMetrics();
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error loading dashboard overview:', error);
        // Use sample data for demo
        dashboardData = getSampleDashboardData();
        updateMetrics();
    }
}

/**
 * Load scan history with pagination and filtering
 */
async function loadScanHistory(page = 1, filter = 'all') {
    try {
        const params = new URLSearchParams({
            page: page,
            per_page: 10,
            sort_by: 'created_at',
            sort_order: 'desc'
        });
        
        if (filter !== 'all') {
            if (filter === 'completed') {
                params.append('status', 'completed');
            } else if (filter === 'recent') {
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                params.append('date_from', weekAgo.toISOString());
            }
        }
        
        const response = await fetch(
            `${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.SCAN_HISTORY}?${params}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const data = await response.json();
        
        if (data.success) {
            scanHistory = data.data.scans;
            renderScanHistory();
            renderPagination(data.data.pagination);
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error loading scan history:', error);
        // Use sample data for demo
        scanHistory = getSampleScanHistory();
        renderScanHistory();
    }
}

/**
 * Update dashboard metrics
 */
function updateMetrics() {
    if (!dashboardData) return;
    
    const summary = dashboardData.summary;
    
    // Update metric values
    elements.totalScans.textContent = summary.total_scans || 0;
    elements.averageScore.textContent = `${Math.round(summary.average_score || 0)}%`;
    elements.implementationRate.textContent = `${Math.round(dashboardData.suggestions?.implementation_rate || 0)}%`;
    elements.premiumUsage.textContent = `${Math.round(dashboardData.premium?.usage_rate || 0)}%`;
    
    // Update change indicators
    updateChangeIndicator(elements.scansChange, summary.total_scans > 0 ? '+12%' : '0%', true);
    updateChangeIndicator(elements.scoreChange, summary.score_improvement || 0, summary.score_improvement > 0);
    updateChangeIndicator(elements.implementationChange, '+8%', true);
    updateChangeIndicator(elements.premiumChange, '+15%', true);
}

/**
 * Update change indicator
 */
function updateChangeIndicator(element, value, isPositive) {
    element.textContent = `${isPositive ? '+' : ''}${value}${typeof value === 'number' ? '%' : ''}`;
    element.className = `metric-change ${isPositive ? 'positive' : 'negative'}`;
}

/**
 * Initialize charts
 */
function initializeCharts() {
    initializeScoreChart();
    initializeDistributionChart();
}

/**
 * Initialize score trends chart
 */
function initializeScoreChart() {
    const ctx = elements.scoreChart.getContext('2d');
    
    // Sample data for demo
    const labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
    const scores = [65, 72, 78, 85];
    
    charts.scoreChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Average Score',
                data: scores,
                borderColor: CONFIG.CHART_COLORS.primary,
                backgroundColor: CONFIG.CHART_COLORS.primary + '20',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: CONFIG.CHART_COLORS.primary,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: '#f0f0f0'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

/**
 * Initialize score distribution chart
 */
function initializeDistributionChart() {
    const ctx = elements.distributionChart.getContext('2d');
    
    // Sample data for demo
    const data = [1, 2, 4, 8, 5]; // Distribution across score ranges
    const labels = ['0-20', '21-40', '41-60', '61-80', '81-100'];
    
    charts.distributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    CONFIG.CHART_COLORS.danger,
                    CONFIG.CHART_COLORS.warning,
                    CONFIG.CHART_COLORS.info,
                    CONFIG.CHART_COLORS.primary,
                    CONFIG.CHART_COLORS.success
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

/**
 * Render scan history
 */
function renderScanHistory() {
    if (!scanHistory || scanHistory.length === 0) {
        showEmptyState();
        return;
    }
    
    const historyHtml = scanHistory.map(scan => {
        const scoreClass = getScoreClass(scan.overall_match_score);
        const statusClass = `status-${scan.scan_status}`;
        const createdDate = new Date(scan.created_at).toLocaleDateString();
        
        return `
            <div class="scan-history-card card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <h6 class="fw-bold mb-0 me-3">${scan.scan_name}</h6>
                                <span class="scan-status ${statusClass}">${scan.scan_status}</span>
                                ${scan.is_bookmarked ? '<i class="fas fa-bookmark text-warning ms-2"></i>' : ''}
                            </div>
                            <p class="text-muted mb-1">${scan.job_title} at ${scan.company_name}</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>${createdDate}
                            </small>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="score-badge ${scoreClass}">
                                ${Math.round(scan.overall_match_score)}%
                            </div>
                            <small class="text-muted d-block mt-1">Match Score</small>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="d-flex justify-content-end align-items-center gap-2">
                                <button class="bookmark-btn ${scan.is_bookmarked ? 'bookmarked' : ''}" 
                                        onclick="toggleBookmark('${scan.id}')">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="viewScanDetails('${scan.id}')">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="editScan('${scan.id}')">
                                            <i class="fas fa-edit me-2"></i>Edit
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteScan('${scan.id}')">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <small class="text-muted">Suggestions: </small>
                            <span class="fw-bold">${scan.total_suggestions}</span>
                            <small class="text-success">(${scan.implemented_suggestions} implemented)</small>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">Keywords Found: </small>
                            <span class="fw-bold">${scan.total_keywords_found}</span>
                        </div>
                        <div class="col-md-4">
                            ${scan.has_premium_suggestions ? 
                                '<span class="badge bg-warning"><i class="fas fa-crown me-1"></i>Premium</span>' : 
                                '<span class="badge bg-secondary">Basic</span>'
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    elements.scanHistorySection.innerHTML = historyHtml;
}

/**
 * Get score class based on score value
 */
function getScoreClass(score) {
    if (score >= 85) return 'score-excellent';
    if (score >= 70) return 'score-good';
    if (score >= 50) return 'score-average';
    return 'score-poor';
}

/**
 * Set active filter
 */
function setActiveFilter(filter) {
    document.querySelectorAll('.btn-filter').forEach(btn => {
        btn.classList.remove('active');
    });
    
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
    currentFilter = filter;
}

/**
 * Render pagination
 */
function renderPagination(pagination) {
    if (!pagination || pagination.pages <= 1) {
        elements.paginationContainer.innerHTML = '';
        return;
    }
    
    let paginationHtml = '<nav><ul class="pagination">';
    
    // Previous button
    if (pagination.has_prev) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadScanHistory(${pagination.current_page - 1}, '${currentFilter}')">Previous</a>
        </li>`;
    }
    
    // Page numbers
    for (let i = 1; i <= pagination.pages; i++) {
        const isActive = i === pagination.current_page;
        paginationHtml += `<li class="page-item ${isActive ? 'active' : ''}">
            <a class="page-link" href="#" onclick="loadScanHistory(${i}, '${currentFilter}')">${i}</a>
        </li>`;
    }
    
    // Next button
    if (pagination.has_next) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" onclick="loadScanHistory(${pagination.current_page + 1}, '${currentFilter}')">Next</a>
        </li>`;
    }
    
    paginationHtml += '</ul></nav>';
    elements.paginationContainer.innerHTML = paginationHtml;
}

/**
 * Toggle bookmark status
 */
async function toggleBookmark(scanId) {
    try {
        const response = await fetch(
            `${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.SCAN_UPDATE}/${scanId}`,
            {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    is_bookmarked: true // Toggle logic would be implemented here
                })
            }
        );
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess('Bookmark updated successfully');
            loadScanHistory(currentPage, currentFilter);
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error toggling bookmark:', error);
        showError('Failed to update bookmark.');
    }
}

/**
 * Export report
 */
async function exportReport() {
    try {
        showSuccess('Report export feature coming soon!');
        // Implementation would go here
    } catch (error) {
        console.error('Error exporting report:', error);
        showError('Failed to export report.');
    }
}

/**
 * Utility functions
 */
function showLoading(show) {
    elements.loadingSpinner.style.display = show ? 'block' : 'none';
    elements.metricsSection.style.display = show ? 'none' : 'block';
}

function showEmptyState() {
    elements.emptyState.style.display = 'block';
    elements.scanHistorySection.innerHTML = '';
}

function hideEmptyState() {
    elements.emptyState.style.display = 'none';
}

function showSuccess(message) {
    alert('Success: ' + message);
}

function showError(message) {
    alert('Error: ' + message);
}

function logout() {
    localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_DATA);
    window.location.href = '../US-01-User-Registration/frontend/us01_home.html';
}

/**
 * Sample data functions for demo
 */
function getSampleDashboardData() {
    return {
        summary: {
            total_scans: 15,
            completed_scans: 14,
            success_rate: 93.3,
            average_score: 75.5,
            highest_score: 89.2,
            score_improvement: 12.3
        },
        suggestions: {
            implementation_rate: 68.5
        },
        premium: {
            usage_rate: 60.0
        }
    };
}

function getSampleScanHistory() {
    return [
        {
            id: 'scan-1',
            scan_name: 'Resume Analysis #1',
            job_title: 'Software Developer',
            company_name: 'TechCorp',
            overall_match_score: 85.5,
            scan_status: 'completed',
            total_suggestions: 8,
            implemented_suggestions: 6,
            total_keywords_found: 15,
            has_premium_suggestions: true,
            is_bookmarked: true,
            created_at: new Date().toISOString()
        },
        {
            id: 'scan-2',
            scan_name: 'Resume Analysis #2',
            job_title: 'Frontend Developer',
            company_name: 'StartupXYZ',
            overall_match_score: 72.3,
            scan_status: 'completed',
            total_suggestions: 10,
            implemented_suggestions: 4,
            total_keywords_found: 12,
            has_premium_suggestions: false,
            is_bookmarked: false,
            created_at: new Date(Date.now() - 86400000).toISOString()
        }
    ];
}

/**
 * Create test token for development
 */
async function createTestToken() {
    try {
        const response = await fetch('http://localhost:5008/api/dev/create-test-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: 'test-user-123',
                is_premium: true
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);
            
            currentUser = {
                id: data.user_id,
                email: '<EMAIL>',
                is_premium: data.is_premium
            };
            localStorage.setItem(CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(currentUser));
            
            updateUserInterface();
        }
    } catch (error) {
        console.error('Error creating test token:', error);
    }
}

// Make functions available globally for onclick handlers
window.toggleBookmark = toggleBookmark;
window.viewScanDetails = function(scanId) { console.log('View details:', scanId); };
window.editScan = function(scanId) { console.log('Edit scan:', scanId); };
window.deleteScan = function(scanId) { console.log('Delete scan:', scanId); };
window.loadScanHistory = loadScanHistory;
