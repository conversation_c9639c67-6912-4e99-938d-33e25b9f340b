"""
US-10: Account Settings Model
=============================

This module defines the database models for account settings and user management
in the Dr. Resume application.

Models:
- UserProfile: Extended user profile information
- UserPreferences: User preferences and settings
- SubscriptionPlan: Available subscription plans
- UserSubscription: User subscription details
- AccountActivity: Account activity logging

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import uuid
from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.dialects.postgresql import UUID, JSON, INET
from enum import Enum
import bcrypt

# Initialize SQLAlchemy (will be configured in main app)
db = SQLAlchemy()


class SubscriptionStatus(Enum):
    """Enumeration for subscription status"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    TRIAL = "trial"


class ActivityType(Enum):
    """Enumeration for account activity types"""
    LOGIN = "login"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    EMAIL_CHANGE = "email_change"
    PROFILE_UPDATE = "profile_update"
    SUBSCRIPTION_CHANGE = "subscription_change"
    ACCOUNT_DELETION = "account_deletion"
    SECURITY_SETTING_CHANGE = "security_setting_change"


class UserProfile(db.Model):
    """
    Extended user profile information
    """
    __tablename__ = 'user_profiles'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User reference (from US-01)
    user_id = db.Column(UUID(as_uuid=True), nullable=False, unique=True, index=True)
    
    # Personal Information
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    display_name = db.Column(db.String(100))
    bio = db.Column(db.Text)
    
    # Contact Information
    phone_number = db.Column(db.String(20))
    country = db.Column(db.String(2))  # ISO country code
    timezone = db.Column(db.String(50))
    language = db.Column(db.String(5), default='en')  # ISO language code
    
    # Professional Information
    job_title = db.Column(db.String(100))
    company = db.Column(db.String(100))
    industry = db.Column(db.String(50))
    experience_years = db.Column(db.Integer)
    
    # Profile Picture
    profile_picture_url = db.Column(db.String(500))
    profile_picture_filename = db.Column(db.String(255))
    
    # Social Links
    linkedin_url = db.Column(db.String(500))
    github_url = db.Column(db.String(500))
    portfolio_url = db.Column(db.String(500))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<UserProfile {self.display_name or self.user_id}>'
    
    def get_full_name(self):
        """Get user's full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.display_name or "User"
    
    def to_dict(self):
        """Convert user profile to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'first_name': self.first_name,
            'last_name': self.last_name,
            'display_name': self.display_name,
            'full_name': self.get_full_name(),
            'bio': self.bio,
            'phone_number': self.phone_number,
            'country': self.country,
            'timezone': self.timezone,
            'language': self.language,
            'job_title': self.job_title,
            'company': self.company,
            'industry': self.industry,
            'experience_years': self.experience_years,
            'profile_picture_url': self.profile_picture_url,
            'linkedin_url': self.linkedin_url,
            'github_url': self.github_url,
            'portfolio_url': self.portfolio_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class UserPreferences(db.Model):
    """
    User preferences and settings
    """
    __tablename__ = 'user_preferences'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User reference
    user_id = db.Column(UUID(as_uuid=True), nullable=False, unique=True, index=True)
    
    # Notification Preferences
    email_notifications = db.Column(db.Boolean, default=True)
    marketing_emails = db.Column(db.Boolean, default=False)
    security_alerts = db.Column(db.Boolean, default=True)
    scan_completion_notifications = db.Column(db.Boolean, default=True)
    weekly_reports = db.Column(db.Boolean, default=False)
    
    # Privacy Settings
    profile_visibility = db.Column(db.String(20), default='private')  # public, private, contacts
    data_sharing = db.Column(db.Boolean, default=False)
    analytics_tracking = db.Column(db.Boolean, default=True)
    
    # Application Preferences
    theme = db.Column(db.String(20), default='light')  # light, dark, auto
    dashboard_layout = db.Column(db.String(20), default='default')
    items_per_page = db.Column(db.Integer, default=10)
    auto_save = db.Column(db.Boolean, default=True)
    
    # AI and Processing Preferences
    ai_suggestions_enabled = db.Column(db.Boolean, default=True)
    processing_quality = db.Column(db.String(20), default='standard')  # fast, standard, detailed
    auto_keyword_extraction = db.Column(db.Boolean, default=True)
    
    # Security Preferences
    two_factor_enabled = db.Column(db.Boolean, default=False)
    session_timeout_minutes = db.Column(db.Integer, default=60)
    login_notifications = db.Column(db.Boolean, default=True)
    
    # Custom Preferences (JSON field for extensibility)
    custom_preferences = db.Column(JSON, default=dict)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<UserPreferences {self.user_id}>'
    
    def to_dict(self):
        """Convert user preferences to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'notifications': {
                'email_notifications': self.email_notifications,
                'marketing_emails': self.marketing_emails,
                'security_alerts': self.security_alerts,
                'scan_completion_notifications': self.scan_completion_notifications,
                'weekly_reports': self.weekly_reports
            },
            'privacy': {
                'profile_visibility': self.profile_visibility,
                'data_sharing': self.data_sharing,
                'analytics_tracking': self.analytics_tracking
            },
            'application': {
                'theme': self.theme,
                'dashboard_layout': self.dashboard_layout,
                'items_per_page': self.items_per_page,
                'auto_save': self.auto_save
            },
            'ai_processing': {
                'ai_suggestions_enabled': self.ai_suggestions_enabled,
                'processing_quality': self.processing_quality,
                'auto_keyword_extraction': self.auto_keyword_extraction
            },
            'security': {
                'two_factor_enabled': self.two_factor_enabled,
                'session_timeout_minutes': self.session_timeout_minutes,
                'login_notifications': self.login_notifications
            },
            'custom_preferences': self.custom_preferences,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class SubscriptionPlan(db.Model):
    """
    Available subscription plans
    """
    __tablename__ = 'subscription_plans'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Plan Information
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    price_monthly = db.Column(db.Numeric(10, 2))
    price_yearly = db.Column(db.Numeric(10, 2))
    
    # Plan Features
    max_scans_per_month = db.Column(db.Integer)
    ai_suggestions_included = db.Column(db.Boolean, default=False)
    premium_templates = db.Column(db.Boolean, default=False)
    priority_support = db.Column(db.Boolean, default=False)
    advanced_analytics = db.Column(db.Boolean, default=False)
    export_capabilities = db.Column(db.Boolean, default=False)
    
    # Plan Configuration
    features = db.Column(JSON, default=list)  # List of feature names
    limits = db.Column(JSON, default=dict)    # Various limits and quotas
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    sort_order = db.Column(db.Integer, default=0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<SubscriptionPlan {self.name}>'
    
    def to_dict(self):
        """Convert subscription plan to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'pricing': {
                'monthly': float(self.price_monthly) if self.price_monthly else None,
                'yearly': float(self.price_yearly) if self.price_yearly else None
            },
            'features': {
                'max_scans_per_month': self.max_scans_per_month,
                'ai_suggestions_included': self.ai_suggestions_included,
                'premium_templates': self.premium_templates,
                'priority_support': self.priority_support,
                'advanced_analytics': self.advanced_analytics,
                'export_capabilities': self.export_capabilities
            },
            'feature_list': self.features,
            'limits': self.limits,
            'is_active': self.is_active,
            'sort_order': self.sort_order
        }


class UserSubscription(db.Model):
    """
    User subscription details
    """
    __tablename__ = 'user_subscriptions'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User and plan references
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    plan_id = db.Column(UUID(as_uuid=True), db.ForeignKey('subscription_plans.id'), nullable=False)
    
    # Subscription Details
    status = db.Column(db.Enum(SubscriptionStatus), default=SubscriptionStatus.INACTIVE)
    billing_cycle = db.Column(db.String(20), default='monthly')  # monthly, yearly
    
    # Dates
    start_date = db.Column(db.DateTime, default=datetime.utcnow)
    end_date = db.Column(db.DateTime)
    trial_end_date = db.Column(db.DateTime)
    next_billing_date = db.Column(db.DateTime)
    
    # Payment Information
    stripe_subscription_id = db.Column(db.String(100))
    stripe_customer_id = db.Column(db.String(100))
    last_payment_amount = db.Column(db.Numeric(10, 2))
    last_payment_date = db.Column(db.DateTime)
    
    # Usage Tracking
    scans_used_this_month = db.Column(db.Integer, default=0)
    ai_suggestions_used = db.Column(db.Integer, default=0)
    
    # Cancellation
    cancelled_at = db.Column(db.DateTime)
    cancellation_reason = db.Column(db.String(200))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    plan = db.relationship('SubscriptionPlan', backref='subscriptions')
    
    def __repr__(self):
        return f'<UserSubscription {self.user_id}: {self.status.value}>'
    
    def is_active(self):
        """Check if subscription is currently active"""
        return (
            self.status == SubscriptionStatus.ACTIVE and
            (self.end_date is None or self.end_date > datetime.utcnow())
        )
    
    def is_trial(self):
        """Check if subscription is in trial period"""
        return (
            self.status == SubscriptionStatus.TRIAL and
            self.trial_end_date and
            self.trial_end_date > datetime.utcnow()
        )
    
    def days_remaining(self):
        """Get days remaining in subscription"""
        if self.end_date:
            remaining = self.end_date - datetime.utcnow()
            return max(0, remaining.days)
        return None
    
    def to_dict(self):
        """Convert user subscription to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'plan': self.plan.to_dict() if self.plan else None,
            'status': self.status.value,
            'billing_cycle': self.billing_cycle,
            'dates': {
                'start_date': self.start_date.isoformat() if self.start_date else None,
                'end_date': self.end_date.isoformat() if self.end_date else None,
                'trial_end_date': self.trial_end_date.isoformat() if self.trial_end_date else None,
                'next_billing_date': self.next_billing_date.isoformat() if self.next_billing_date else None
            },
            'payment': {
                'last_amount': float(self.last_payment_amount) if self.last_payment_amount else None,
                'last_payment_date': self.last_payment_date.isoformat() if self.last_payment_date else None
            },
            'usage': {
                'scans_used_this_month': self.scans_used_this_month,
                'ai_suggestions_used': self.ai_suggestions_used
            },
            'is_active': self.is_active(),
            'is_trial': self.is_trial(),
            'days_remaining': self.days_remaining(),
            'cancelled_at': self.cancelled_at.isoformat() if self.cancelled_at else None,
            'cancellation_reason': self.cancellation_reason
        }


class AccountActivity(db.Model):
    """
    Account activity logging
    """
    __tablename__ = 'account_activities'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User reference
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Activity Information
    activity_type = db.Column(db.Enum(ActivityType), nullable=False)
    description = db.Column(db.String(200))
    details = db.Column(JSON, default=dict)
    
    # Request Information
    ip_address = db.Column(INET)
    user_agent = db.Column(db.String(500))
    
    # Success/Failure
    success = db.Column(db.Boolean, default=True)
    error_message = db.Column(db.String(500))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        return f'<AccountActivity {self.user_id}: {self.activity_type.value}>'
    
    def to_dict(self):
        """Convert account activity to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'activity_type': self.activity_type.value,
            'description': self.description,
            'details': self.details,
            'ip_address': str(self.ip_address) if self.ip_address else None,
            'user_agent': self.user_agent,
            'success': self.success,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
