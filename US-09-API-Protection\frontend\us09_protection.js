/**
 * US-09: API Protection Frontend JavaScript
 * =========================================
 * 
 * This file handles JWT authentication, role-based access control,
 * and demonstrates API protection features.
 * 
 * Features:
 * - JWT token management and validation
 * - Auto redirect to login if token invalid
 * - Hide dashboard during auth checks
 * - Block premium UI unless token has role: premium
 * - Role-based UI rendering
 * 
 * Author: Dr. Resume Development Team
 * Date: 2025-07-23
 * Version: 1.0.0
 */

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://localhost:5009/api',
    ENDPOINTS: {
        CHECK_TOKEN: '/auth/check-token',
        USER_INFO: '/auth/user-info',
        BASIC_PROTECTED: '/protected/basic',
        PREMIUM_PROTECTED: '/protected/premium',
        ADMIN_PROTECTED: '/protected/admin',
        SECURITY_DASHBOARD: '/security/dashboard',
        CREATE_TEST_TOKEN: '/dev/create-test-token'
    },
    STORAGE_KEYS: {
        AUTH_TOKEN: 'dr_resume_token',
        USER_DATA: 'dr_resume_user'
    }
};

// Global state
let currentUser = null;
let authToken = null;
let isAuthenticated = false;
let userRole = 'basic';

// DOM elements
let elements = {};

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    setupEventListeners();
    checkAuthentication();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        authStatus: document.getElementById('authStatus'),
        authSpinner: document.getElementById('authSpinner'),
        authStatusTitle: document.getElementById('authStatusTitle'),
        authStatusMessage: document.getElementById('authStatusMessage'),
        userInfoCard: document.getElementById('userInfoCard'),
        userInfoContent: document.getElementById('userInfoContent'),
        securityDashboard: document.getElementById('securityDashboard'),
        premiumUpgrade: document.getElementById('premiumUpgrade'),
        loginBtn: document.getElementById('loginBtn'),
        logoutBtn: document.getElementById('logoutBtn'),
        userTypeSelect: document.getElementById('userTypeSelect'),
        
        // Response areas
        basicResponse: document.getElementById('basicResponse'),
        premiumResponse: document.getElementById('premiumResponse'),
        adminResponse: document.getElementById('adminResponse'),
        tokenResponse: document.getElementById('tokenResponse'),
        dashboardResponse: document.getElementById('dashboardResponse')
    };
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    elements.loginBtn.addEventListener('click', showLoginModal);
    elements.logoutBtn.addEventListener('click', logout);
}

/**
 * Check authentication status on page load
 */
async function checkAuthentication() {
    showAuthChecking();
    
    // Get token from localStorage
    authToken = localStorage.getItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    
    if (!authToken) {
        showUnauthenticated();
        return;
    }
    
    try {
        // Validate token with server
        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.CHECK_TOKEN}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success && data.data.valid) {
            // Token is valid
            currentUser = data.data;
            isAuthenticated = true;
            userRole = currentUser.claims.role || 'basic';
            
            showAuthenticated();
            updateUIBasedOnRole();
            loadUserInfo();
        } else {
            // Token is invalid
            clearAuthData();
            showUnauthenticated();
        }
        
    } catch (error) {
        console.error('Error checking authentication:', error);
        clearAuthData();
        showUnauthenticated();
    }
}

/**
 * Show authentication checking state
 */
function showAuthChecking() {
    elements.authStatus.className = 'auth-status checking';
    elements.authSpinner.style.display = 'inline-block';
    elements.authStatusTitle.textContent = 'Checking Authentication...';
    elements.authStatusMessage.textContent = 'Verifying your access token...';
    
    // Hide all protected content during check
    hideProtectedContent();
}

/**
 * Show authenticated state
 */
function showAuthenticated() {
    elements.authStatus.className = 'auth-status authenticated';
    elements.authSpinner.style.display = 'none';
    elements.authStatusTitle.textContent = 'Authentication Successful';
    elements.authStatusMessage.textContent = `Welcome back! You are logged in as ${userRole} user.`;
    
    elements.loginBtn.classList.add('hidden');
    elements.logoutBtn.classList.remove('hidden');
    
    showProtectedContent();
}

/**
 * Show unauthenticated state
 */
function showUnauthenticated() {
    elements.authStatus.className = 'auth-status unauthenticated';
    elements.authSpinner.style.display = 'none';
    elements.authStatusTitle.textContent = 'Authentication Required';
    elements.authStatusMessage.textContent = 'Please login to access protected features.';
    
    elements.loginBtn.classList.remove('hidden');
    elements.logoutBtn.classList.add('hidden');
    
    hideProtectedContent();
}

/**
 * Hide protected content during authentication checks
 */
function hideProtectedContent() {
    elements.userInfoCard.classList.add('hidden');
    elements.securityDashboard.classList.add('hidden');
    elements.premiumUpgrade.classList.add('hidden');
}

/**
 * Show protected content after successful authentication
 */
function showProtectedContent() {
    elements.userInfoCard.classList.remove('hidden');
}

/**
 * Update UI based on user role
 */
function updateUIBasedOnRole() {
    if (!isAuthenticated) return;
    
    // Show/hide premium upgrade message
    if (currentUser.is_premium) {
        elements.premiumUpgrade.classList.add('hidden');
    } else {
        // Will be shown when premium route is tested
    }
    
    // Show/hide admin dashboard
    if (currentUser.is_admin) {
        elements.securityDashboard.classList.remove('hidden');
    } else {
        elements.securityDashboard.classList.add('hidden');
    }
}

/**
 * Load and display user information
 */
async function loadUserInfo() {
    try {
        const response = await makeAuthenticatedRequest(CONFIG.ENDPOINTS.USER_INFO);
        const data = await response.json();
        
        if (data.success) {
            displayUserInfo(data.data);
        }
        
    } catch (error) {
        console.error('Error loading user info:', error);
    }
}

/**
 * Display user information in the UI
 */
function displayUserInfo(userInfo) {
    const html = `
        <div class="row">
            <div class="col-md-6">
                <p><strong>User ID:</strong> ${userInfo.user_id}</p>
                <p><strong>Role:</strong> <span class="badge bg-primary">${userInfo.claims.role || 'basic'}</span></p>
                <p><strong>Premium:</strong> ${userInfo.is_premium ? 
                    '<span class="badge bg-warning">Yes</span>' : 
                    '<span class="badge bg-secondary">No</span>'}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Admin:</strong> ${userInfo.is_admin ? 
                    '<span class="badge bg-danger">Yes</span>' : 
                    '<span class="badge bg-secondary">No</span>'}</p>
                <p><strong>Authenticated:</strong> <span class="badge bg-success">Yes</span></p>
            </div>
        </div>
    `;
    
    elements.userInfoContent.innerHTML = html;
}

/**
 * Test basic protection (JWT required)
 */
async function testBasicProtection() {
    await testProtectedRoute(
        CONFIG.ENDPOINTS.BASIC_PROTECTED,
        elements.basicResponse,
        'Basic Protection Test'
    );
}

/**
 * Test premium protection (Premium role required)
 */
async function testPremiumProtection() {
    const result = await testProtectedRoute(
        CONFIG.ENDPOINTS.PREMIUM_PROTECTED,
        elements.premiumResponse,
        'Premium Protection Test'
    );
    
    // Show upgrade message if premium required
    if (result && result.error === 'premium_required') {
        elements.premiumUpgrade.classList.remove('hidden');
    }
}

/**
 * Test admin protection (Admin role required)
 */
async function testAdminProtection() {
    await testProtectedRoute(
        CONFIG.ENDPOINTS.ADMIN_PROTECTED,
        elements.adminResponse,
        'Admin Protection Test'
    );
}

/**
 * Test a protected route and display results
 */
async function testProtectedRoute(endpoint, responseElement, testName) {
    try {
        if (!isAuthenticated) {
            showLoginModal();
            return;
        }
        
        responseElement.classList.remove('hidden');
        responseElement.innerHTML = `<div class="text-info">Testing ${testName}...</div>`;
        
        const response = await makeAuthenticatedRequest(endpoint);
        const data = await response.json();
        
        // Format and display response
        const formattedResponse = JSON.stringify(data, null, 2);
        const statusClass = data.success ? 'text-success' : 'text-danger';
        
        responseElement.innerHTML = `
            <div class="${statusClass}">
                <strong>Status:</strong> ${response.status} ${response.statusText}
            </div>
            <pre>${formattedResponse}</pre>
        `;
        
        return data;
        
    } catch (error) {
        console.error(`Error testing ${testName}:`, error);
        responseElement.innerHTML = `<div class="text-danger">Error: ${error.message}</div>`;
    }
}

/**
 * Check token validity
 */
async function checkToken() {
    try {
        elements.tokenResponse.classList.remove('hidden');
        elements.tokenResponse.innerHTML = '<div class="text-info">Checking token...</div>';
        
        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.CHECK_TOKEN}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        const formattedResponse = JSON.stringify(data, null, 2);
        
        elements.tokenResponse.innerHTML = `
            <div class="text-info">
                <strong>Token Check Result:</strong>
            </div>
            <pre>${formattedResponse}</pre>
        `;
        
    } catch (error) {
        console.error('Error checking token:', error);
        elements.tokenResponse.innerHTML = `<div class="text-danger">Error: ${error.message}</div>`;
    }
}

/**
 * Get user information
 */
async function getUserInfo() {
    await testProtectedRoute(
        CONFIG.ENDPOINTS.USER_INFO,
        elements.tokenResponse,
        'User Info Retrieval'
    );
}

/**
 * Refresh token (create new test token)
 */
async function refreshToken() {
    try {
        elements.tokenResponse.classList.remove('hidden');
        elements.tokenResponse.innerHTML = '<div class="text-info">Refreshing token...</div>';
        
        const userType = elements.userTypeSelect.value;
        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.CREATE_TEST_TOKEN}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: `test-${userType}-user`,
                is_premium: userType === 'premium' || userType === 'admin',
                role: userType
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);
            
            elements.tokenResponse.innerHTML = `
                <div class="text-success">
                    <strong>Token Refreshed Successfully!</strong>
                </div>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            
            // Re-check authentication
            setTimeout(() => {
                checkAuthentication();
            }, 1000);
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error refreshing token:', error);
        elements.tokenResponse.innerHTML = `<div class="text-danger">Error: ${error.message}</div>`;
    }
}

/**
 * Clear token from storage
 */
function clearToken() {
    clearAuthData();
    elements.tokenResponse.classList.remove('hidden');
    elements.tokenResponse.innerHTML = '<div class="text-warning">Token cleared from storage.</div>';
    checkAuthentication();
}

/**
 * Load security dashboard (admin only)
 */
async function loadSecurityDashboard() {
    await testProtectedRoute(
        CONFIG.ENDPOINTS.SECURITY_DASHBOARD,
        elements.dashboardResponse,
        'Security Dashboard'
    );
}

/**
 * Show login modal
 */
function showLoginModal() {
    const modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
}

/**
 * Perform login with selected user type
 */
async function performLogin() {
    const userType = elements.userTypeSelect.value;
    
    try {
        const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.CREATE_TEST_TOKEN}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: `test-${userType}-user`,
                is_premium: userType === 'premium' || userType === 'admin',
                role: userType
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            modal.hide();
            
            // Re-check authentication
            checkAuthentication();
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error during login:', error);
        alert('Login failed: ' + error.message);
    }
}

/**
 * Logout user
 */
function logout() {
    clearAuthData();
    checkAuthentication();
}

/**
 * Upgrade account to premium
 */
function upgradeAccount() {
    alert('Upgrade functionality would redirect to payment page in a real application.');
}

/**
 * Make authenticated request
 */
async function makeAuthenticatedRequest(endpoint, options = {}) {
    if (!authToken) {
        throw new Error('No authentication token available');
    }
    
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    const response = await fetch(`${CONFIG.API_BASE_URL}${endpoint}`, mergedOptions);
    
    // Check if token is invalid and redirect to login
    if (response.status === 401) {
        clearAuthData();
        showUnauthenticated();
        showLoginModal();
        throw new Error('Authentication required');
    }
    
    return response;
}

/**
 * Clear authentication data
 */
function clearAuthData() {
    authToken = null;
    currentUser = null;
    isAuthenticated = false;
    userRole = 'basic';
    
    localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_DATA);
}

// Make functions available globally
window.testBasicProtection = testBasicProtection;
window.testPremiumProtection = testPremiumProtection;
window.testAdminProtection = testAdminProtection;
window.checkToken = checkToken;
window.getUserInfo = getUserInfo;
window.refreshToken = refreshToken;
window.clearToken = clearToken;
window.loadSecurityDashboard = loadSecurityDashboard;
window.performLogin = performLogin;
window.upgradeAccount = upgradeAccount;
