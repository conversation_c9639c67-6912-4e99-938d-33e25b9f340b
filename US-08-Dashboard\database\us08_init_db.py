"""
US-08: Dashboard Database Initialization
========================================

This script initializes the database for the dashboard feature in the Dr. Resume application.
It creates tables, indexes, views, and optionally inserts sample data for development and testing.

Usage:
    python us08_init_db.py [--sample-data] [--reset]

Options:
    --sample-data: Insert sample data for development
    --reset: Drop existing tables before creating new ones

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import uuid
import random

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseInitializer:
    """Database initialization class for US-08 Dashboard"""
    
    def __init__(self):
        """Initialize database connection parameters"""
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'dr_resume_db'),
            'user': os.getenv('DB_USER', 'dr_resume_user'),
            'password': os.getenv('DB_PASSWORD', 'password')
        }
        
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            self.cursor = self.connection.cursor()
            logger.info("Database connection established")
            return True
        except psycopg2.Error as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Database connection closed")
    
    def execute_sql_file(self, file_path):
        """Execute SQL commands from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # Split by semicolon and execute each statement
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement and not statement.startswith('--'):
                    try:
                        self.cursor.execute(statement)
                        logger.debug(f"Executed: {statement[:50]}...")
                    except psycopg2.Error as e:
                        logger.warning(f"Statement failed: {e}")
                        # Continue with other statements
            
            logger.info(f"Successfully executed SQL file: {file_path}")
            return True
            
        except FileNotFoundError:
            logger.error(f"SQL file not found: {file_path}")
            return False
        except Exception as e:
            logger.error(f"Error executing SQL file: {e}")
            return False
    
    def create_tables(self, reset=False):
        """Create database tables"""
        try:
            if reset:
                logger.info("Dropping existing tables...")
                self.drop_tables()
            
            # Get the directory of this script
            script_dir = os.path.dirname(os.path.abspath(__file__))
            schema_file = os.path.join(script_dir, 'us08_schema.sql')
            
            logger.info("Creating tables from schema file...")
            success = self.execute_sql_file(schema_file)
            
            if success:
                logger.info("Tables created successfully")
            else:
                logger.error("Failed to create tables")
            
            return success
            
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def drop_tables(self):
        """Drop existing tables"""
        try:
            drop_statements = [
                "DROP VIEW IF EXISTS user_dashboard_summary CASCADE;",
                "DROP VIEW IF EXISTS recent_scan_activity CASCADE;",
                "DROP TABLE IF EXISTS export_history CASCADE;",
                "DROP TABLE IF EXISTS user_activity CASCADE;",
                "DROP TABLE IF EXISTS dashboard_analytics CASCADE;",
                "DROP TABLE IF EXISTS scan_history CASCADE;",
                "DROP TYPE IF EXISTS export_format CASCADE;",
                "DROP TYPE IF EXISTS scan_status CASCADE;",
                "DROP FUNCTION IF EXISTS cleanup_expired_exports() CASCADE;",
                "DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;"
            ]
            
            for statement in drop_statements:
                try:
                    self.cursor.execute(statement)
                    logger.debug(f"Executed: {statement}")
                except psycopg2.Error as e:
                    logger.debug(f"Drop statement failed (may not exist): {e}")
            
            logger.info("Existing tables dropped")
            
        except Exception as e:
            logger.error(f"Error dropping tables: {e}")
    
    def insert_sample_data(self):
        """Insert sample data for development and testing"""
        try:
            logger.info("Inserting sample data...")
            
            # Sample user IDs
            sample_user_ids = [
                'sample-user-123',
                'sample-user-456',
                'sample-user-789'
            ]
            
            # Sample companies and job titles
            companies = ['TechCorp', 'StartupXYZ', 'MegaSoft', 'InnovateLab', 'DataDriven Inc']
            job_titles = ['Software Developer', 'Frontend Developer', 'Backend Developer', 
                         'Full Stack Developer', 'Data Scientist', 'DevOps Engineer']
            locations = ['San Francisco, CA', 'New York, NY', 'Seattle, WA', 'Austin, TX', 'Remote']
            job_types = ['full-time', 'part-time', 'contract', 'internship']
            
            # Insert sample scan history
            for user_id in sample_user_ids:
                for i in range(15):  # 15 scans per user
                    scan_id = str(uuid.uuid4())
                    resume_id = str(uuid.uuid4())
                    jd_id = str(uuid.uuid4())
                    
                    # Generate realistic data
                    company = random.choice(companies)
                    job_title = random.choice(job_titles)
                    location = random.choice(locations)
                    job_type = random.choice(job_types)
                    
                    # Scores with some variation
                    base_score = random.uniform(45, 95)
                    overall_score = round(base_score, 2)
                    keyword_score = round(base_score + random.uniform(-10, 10), 2)
                    skill_score = round(base_score + random.uniform(-15, 15), 2)
                    experience_score = round(base_score + random.uniform(-5, 20), 2)
                    
                    # Ensure scores are within bounds
                    keyword_score = max(0, min(100, keyword_score))
                    skill_score = max(0, min(100, skill_score))
                    experience_score = max(0, min(100, experience_score))
                    
                    # Suggestions data
                    total_suggestions = random.randint(3, 12)
                    high_priority = random.randint(1, min(5, total_suggestions))
                    implemented = random.randint(0, total_suggestions)
                    dismissed = random.randint(0, total_suggestions - implemented)
                    
                    # Keywords
                    sample_matched = ['python', 'javascript', 'sql', 'html', 'css']
                    sample_missing = ['react', 'aws', 'docker', 'kubernetes', 'mongodb']
                    
                    matched_keywords = random.sample(sample_matched, random.randint(2, 4))
                    missing_keywords = random.sample(sample_missing, random.randint(1, 3))
                    
                    # Premium features
                    has_premium = random.choice([True, False])
                    premium_cost = round(random.uniform(0.01, 0.05), 4) if has_premium else 0
                    ai_tokens = random.randint(200, 800) if has_premium else 0
                    
                    # Processing time
                    processing_time = round(random.uniform(2.5, 15.0), 3)
                    
                    # Date (spread over last 90 days)
                    days_ago = random.randint(0, 90)
                    created_at = datetime.utcnow() - timedelta(days=days_ago)
                    
                    insert_query = """
                    INSERT INTO scan_history (
                        id, user_id, resume_id, job_description_id,
                        scan_name, scan_description, scan_status,
                        resume_filename, job_title, company_name, job_location, job_type,
                        overall_match_score, keyword_match_score, skill_match_score, experience_match_score,
                        total_keywords_found, missing_keywords_count,
                        matched_keywords, missing_keywords,
                        total_suggestions, high_priority_suggestions, 
                        implemented_suggestions, dismissed_suggestions,
                        has_premium_suggestions, premium_cost, ai_tokens_used,
                        processing_time_seconds, is_bookmarked, user_rating,
                        created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s,
                        %s, %s, %s,
                        %s, %s, %s, %s, %s,
                        %s, %s, %s, %s,
                        %s, %s,
                        %s::jsonb, %s::jsonb,
                        %s, %s,
                        %s, %s,
                        %s, %s, %s,
                        %s, %s, %s,
                        %s, %s
                    );
                    """
                    
                    self.cursor.execute(insert_query, (
                        scan_id, user_id, resume_id, jd_id,
                        f"Resume Analysis #{i+1}", f"Analysis for {job_title} position at {company}", 'completed',
                        f"resume_v{i+1}.pdf", job_title, company, location, job_type,
                        overall_score, keyword_score, skill_score, experience_score,
                        len(matched_keywords), len(missing_keywords),
                        str(matched_keywords), str(missing_keywords),
                        total_suggestions, high_priority,
                        implemented, dismissed,
                        has_premium, premium_cost, ai_tokens,
                        processing_time, i == 0, random.randint(3, 5) if random.random() > 0.7 else None,
                        created_at, created_at
                    ))
            
            # Insert sample user activity
            activity_types = ['login', 'scan_create', 'scan_view', 'dashboard_view', 'export_generate']
            
            for user_id in sample_user_ids:
                for i in range(50):  # 50 activities per user
                    activity_id = str(uuid.uuid4())
                    activity_type = random.choice(activity_types)
                    description = f"User performed {activity_type}"
                    
                    days_ago = random.randint(0, 30)
                    activity_time = datetime.utcnow() - timedelta(days=days_ago, 
                                                                hours=random.randint(0, 23),
                                                                minutes=random.randint(0, 59))
                    
                    activity_query = """
                    INSERT INTO user_activity (
                        id, user_id, activity_type, activity_description,
                        ip_address, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s);
                    """
                    
                    self.cursor.execute(activity_query, (
                        activity_id, user_id, activity_type, description,
                        f"192.168.1.{random.randint(1, 254)}", activity_time
                    ))
            
            logger.info("Sample data inserted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting sample data: {e}")
            return False
    
    def verify_installation(self):
        """Verify that tables were created correctly"""
        try:
            # Check if tables exist
            tables_to_check = ['scan_history', 'dashboard_analytics', 'user_activity', 'export_history']
            
            for table in tables_to_check:
                self.cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    );
                """, (table,))
                
                exists = self.cursor.fetchone()[0]
                if exists:
                    logger.info(f"✓ Table '{table}' exists")
                else:
                    logger.error(f"✗ Table '{table}' does not exist")
                    return False
            
            # Check views
            views_to_check = ['recent_scan_activity', 'user_dashboard_summary']
            
            for view in views_to_check:
                self.cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.views 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    );
                """, (view,))
                
                exists = self.cursor.fetchone()[0]
                if exists:
                    logger.info(f"✓ View '{view}' exists")
                else:
                    logger.warning(f"⚠ View '{view}' does not exist")
            
            # Check record counts
            for table in tables_to_check:
                self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = self.cursor.fetchone()[0]
                logger.info(f"  - {table}: {count} records")
            
            logger.info("Database verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying installation: {e}")
            return False


def main():
    """Main function to initialize the database"""
    parser = argparse.ArgumentParser(description='Initialize US-08 Dashboard database')
    parser.add_argument('--sample-data', action='store_true', 
                       help='Insert sample data for development')
    parser.add_argument('--reset', action='store_true',
                       help='Drop existing tables before creating new ones')
    
    args = parser.parse_args()
    
    logger.info("Starting US-08 Dashboard database initialization...")
    
    # Initialize database
    db_init = DatabaseInitializer()
    
    try:
        # Connect to database
        if not db_init.connect():
            logger.error("Failed to connect to database")
            sys.exit(1)
        
        # Create tables
        if not db_init.create_tables(reset=args.reset):
            logger.error("Failed to create tables")
            sys.exit(1)
        
        # Insert sample data if requested
        if args.sample_data:
            if not db_init.insert_sample_data():
                logger.error("Failed to insert sample data")
                sys.exit(1)
        
        # Verify installation
        if not db_init.verify_installation():
            logger.error("Database verification failed")
            sys.exit(1)
        
        logger.info("✅ US-08 Dashboard database initialization completed successfully!")
        
        if args.sample_data:
            logger.info("📊 Sample data has been inserted for development")
            logger.info("   - 3 sample users with 15 scans each")
            logger.info("   - 50 user activities per user")
            logger.info("   - Realistic score distributions and trends")
        
        logger.info("🚀 You can now start the US-08 Dashboard service")
        
    except KeyboardInterrupt:
        logger.info("Initialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        db_init.disconnect()


if __name__ == '__main__':
    main()
