# US-08: Dashboard (Scan History) - Python Dependencies
# ======================================================
# 
# This file contains all Python packages required for US-08 Dashboard feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01 through US-07, plus new dashboard requirements

# Core Flask Framework (from previous US)
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (from previous US)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration (from previous US)
Flask-CORS==4.0.0

# Environment Variables Management (from previous US)
python-dotenv==1.0.0

# Date and Time Utilities (from previous US)
python-dateutil==2.8.2

# JSON Handling and Validation (from previous US)
jsonschema==4.19.1

# File Processing (from US-03)
PyPDF2==3.0.1
python-docx==0.8.11

# Natural Language Processing Libraries (from US-05)
spacy==3.6.1
nltk==3.8.1
textblob==0.17.1
regex==2023.8.8

# Matching and Similarity Calculation Libraries (from US-06)
numpy==1.24.3
scipy==1.11.2
scikit-learn==1.3.0
pandas==2.0.3
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# OpenAI API (from US-07)
openai==1.3.5
httpx==0.25.0

# NEW: Dashboard and Analytics Libraries for US-08
# ================================================

# Data visualization and charts
plotly==5.17.0
matplotlib==3.7.2
seaborn==0.12.2

# Data analysis and aggregation
pandas==2.0.3
numpy==1.24.3

# Statistical analysis for dashboard metrics
statsmodels==0.14.0

# Time series analysis for trends
prophet==1.1.4

# Excel export functionality
openpyxl==3.1.2
xlsxwriter==3.1.9

# PDF report generation
reportlab==4.0.4
weasyprint==60.1

# Caching for dashboard performance
redis==5.0.1
flask-caching==2.1.0

# Background task processing for report generation
celery==5.3.4
kombu==5.3.4

# API pagination for large datasets
flask-sqlalchemy-paginate==0.1.0

# Data serialization for API responses
marshmallow==3.20.1
marshmallow-sqlalchemy==0.29.0

# Input validation for dashboard filters
wtforms==3.1.0
flask-wtf==1.2.1

# Rate limiting for dashboard APIs
flask-limiter==3.5.0

# Logging and monitoring
structlog==23.2.0
colorlog==6.7.0

# Performance monitoring
flask-limiter==3.5.0
memory-profiler==0.61.0

# Email notifications for reports
flask-mail==0.9.1

# File compression for exports
zipfile36==0.1.3

# Image processing for charts
pillow==10.0.1

# Calendar and date utilities
calendar==0.1.0

# Timezone handling
pytz==2023.3

# URL generation and routing
flask-restx==1.2.0

# Security headers
flask-talisman==1.1.0

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
requests==2.31.0
responses==0.23.3

# Production Server (optional)
gunicorn==21.2.0

# Security
cryptography==41.0.4

# Configuration management
configparser==6.0.0

# Data validation
email-validator==2.0.0

# Additional utilities
click==8.1.7
itsdangerous==2.1.2

# Built-in Python modules (listed for clarity):
# collections - Specialized container datatypes (built-in)
# decimal - Decimal arithmetic (built-in)
# time - Time-related functions (built-in)
# typing - Type hints (built-in)
# re - Regular expressions (built-in)
# os - Operating system interface (built-in)
# sys - System-specific parameters (built-in)
# datetime - Date and time (built-in)
# json - JSON encoder/decoder (built-in)
# uuid - UUID generation (built-in)
# math - Mathematical functions (built-in)
# statistics - Statistical functions (built-in)
# csv - CSV file reading and writing (built-in)
# io - Core tools for working with streams (built-in)
# base64 - Base64 encoding/decoding (built-in)
