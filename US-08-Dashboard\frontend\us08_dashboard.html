<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Dr. Resume</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
            --card-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .main-container {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .metric-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--dark-text);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-change {
            font-size: 0.85rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        .metric-change.positive {
            color: var(--success-color);
        }

        .metric-change.negative {
            color: var(--danger-color);
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }

        .scan-history-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            border: none;
        }

        .scan-history-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .scan-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-processing {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }

        .score-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1rem;
        }

        .score-excellent {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }

        .score-good {
            background: linear-gradient(45deg, #3498db, #5dade2);
            color: white;
        }

        .score-average {
            background: linear-gradient(45deg, #f39c12, #f7dc6f);
            color: white;
        }

        .score-poor {
            background: linear-gradient(45deg, #e74c3c, #ec7063);
            color: white;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }

        .btn-filter {
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            border: 2px solid var(--secondary-color);
            color: var(--secondary-color);
            background: transparent;
            transition: all 0.3s ease;
        }

        .btn-filter:hover, .btn-filter.active {
            background: var(--secondary-color);
            color: white;
            transform: translateY(-1px);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 3rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }

        .bookmark-btn {
            background: none;
            border: none;
            color: #ffc107;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .bookmark-btn:hover {
            transform: scale(1.2);
        }

        .bookmark-btn.bookmarked {
            color: #ff6b35;
        }

        @media (max-width: 768px) {
            .main-container {
                margin-top: 1rem;
            }
            
            .metric-card {
                margin-bottom: 1rem;
            }
            
            .chart-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-md me-2"></i>
                Dr. Resume
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../US-01-User-Registration/frontend/us01_home.html">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../US-03-Resume-Upload/frontend/us03_upload.html">
                            <i class="fas fa-upload me-1"></i>Upload Resume
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../US-06-Matching-Score/frontend/us06_matching.html">
                            <i class="fas fa-chart-line me-1"></i>Matching Score
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../US-07-Suggestions/frontend/us07_suggestions.html">
                            <i class="fas fa-lightbulb me-1"></i>Suggestions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="userProfile">
                            <i class="fas fa-user me-1"></i>Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-container">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="display-6 fw-bold text-dark">
                            <i class="fas fa-tachometer-alt text-primary me-3"></i>
                            Dashboard
                        </h1>
                        <p class="lead text-muted">Track your resume optimization progress and analytics</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary me-2" id="refreshDashboard">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                        <button class="btn btn-primary" id="exportReportBtn">
                            <i class="fas fa-download me-2"></i>Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Loading dashboard data...</p>
        </div>

        <!-- Metrics Overview -->
        <div class="row mb-4" id="metricsSection">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-icon" style="background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="metric-value" id="totalScans">0</div>
                    <div class="metric-label">Total Scans</div>
                    <div class="metric-change" id="scansChange"></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-icon" style="background: linear-gradient(45deg, var(--success-color), #2ecc71);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="metric-value" id="averageScore">0%</div>
                    <div class="metric-label">Average Score</div>
                    <div class="metric-change" id="scoreChange"></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-icon" style="background: linear-gradient(45deg, var(--warning-color), #f7dc6f);">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="metric-value" id="implementationRate">0%</div>
                    <div class="metric-label">Implementation Rate</div>
                    <div class="metric-change" id="implementationChange"></div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card">
                    <div class="metric-icon" style="background: linear-gradient(45deg, var(--info-color), #5dade2);">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="metric-value" id="premiumUsage">0%</div>
                    <div class="metric-label">Premium Usage</div>
                    <div class="metric-change" id="premiumChange"></div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-chart-area me-2 text-primary"></i>
                        Score Trends
                    </h5>
                    <canvas id="scoreChart" height="100"></canvas>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="chart-container">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        Score Distribution
                    </h5>
                    <canvas id="distributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filter-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-history me-2 text-primary"></i>
                        Scan History
                    </h5>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2 flex-wrap justify-content-md-end">
                        <button class="btn btn-filter active" data-filter="all">All</button>
                        <button class="btn btn-filter" data-filter="completed">Completed</button>
                        <button class="btn btn-filter" data-filter="bookmarked">Bookmarked</button>
                        <button class="btn btn-filter" data-filter="recent">Recent</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scan History Section -->
        <div id="scanHistorySection">
            <!-- Scan history cards will be loaded here -->
        </div>

        <!-- Pagination -->
        <div class="pagination-container" id="paginationContainer">
            <!-- Pagination will be loaded here -->
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-chart-bar"></i>
            <h4>No Scan History</h4>
            <p>Start by uploading a resume and job description to see your analytics.</p>
            <a href="../US-03-Resume-Upload/frontend/us03_upload.html" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>Upload Resume
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us08_dashboard.js"></script>
</body>
</html>
