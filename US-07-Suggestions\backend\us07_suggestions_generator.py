"""
US-07: Suggestions Generator
============================

This module contains the core logic for generating basic keyword suggestions
by analyzing the gap between resume keywords and job description keywords.

Classes:
- SuggestionsGenerator: Main class for generating basic suggestions
- KeywordAnalyzer: Helper class for keyword analysis
- SuggestionFormatter: Helper class for formatting suggestions

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import re
import json
import logging
from typing import List, Dict, Set, Tuple, Optional
from collections import Counter, defaultdict
from datetime import datetime

# NLP libraries
import spacy
import nltk
from textblob import TextBlob
from fuzzywuzzy import fuzz, process

# Data analysis
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Local imports
from us07_suggestions_model import Suggestion, SuggestionType, SuggestionPriority

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KeywordAnalyzer:
    """Helper class for analyzing keywords and their relationships"""
    
    def __init__(self):
        """Initialize the keyword analyzer with NLP models"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
            self.nlp = None
        
        # Download required NLTK data
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('wordnet', quiet=True)
        except Exception as e:
            logger.warning(f"NLTK download failed: {e}")
    
    def extract_keywords(self, text: str, min_length: int = 2) -> Set[str]:
        """Extract keywords from text using multiple methods"""
        keywords = set()
        
        if not text:
            return keywords
        
        # Method 1: spaCy NER and important tokens
        if self.nlp:
            doc = self.nlp(text.lower())
            for token in doc:
                if (not token.is_stop and not token.is_punct and 
                    len(token.text) >= min_length and token.pos_ in ['NOUN', 'ADJ', 'VERB']):
                    keywords.add(token.lemma_)
            
            # Add named entities
            for ent in doc.ents:
                if ent.label_ in ['PERSON', 'ORG', 'PRODUCT', 'SKILL']:
                    keywords.add(ent.text.lower())
        
        # Method 2: Simple regex for technical terms
        tech_pattern = r'\b[A-Z]{2,}(?:\+{1,2}|\#)?|\b[a-zA-Z]+(?:\.[a-zA-Z]+)+\b'
        tech_matches = re.findall(tech_pattern, text)
        keywords.update([match.lower() for match in tech_matches])
        
        # Method 3: Common skill patterns
        skill_patterns = [
            r'\b(?:python|java|javascript|react|angular|vue|node\.?js|sql|html|css)\b',
            r'\b(?:aws|azure|gcp|docker|kubernetes|jenkins|git)\b',
            r'\b(?:machine learning|data science|artificial intelligence|deep learning)\b',
            r'\b(?:project management|agile|scrum|kanban)\b'
        ]
        
        for pattern in skill_patterns:
            matches = re.findall(pattern, text.lower())
            keywords.update(matches)
        
        return keywords
    
    def calculate_keyword_similarity(self, keywords1: Set[str], keywords2: Set[str]) -> float:
        """Calculate similarity between two sets of keywords"""
        if not keywords1 or not keywords2:
            return 0.0
        
        # Jaccard similarity
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        return intersection / union if union > 0 else 0.0
    
    def find_similar_keywords(self, keyword: str, keyword_list: List[str], threshold: int = 80) -> List[str]:
        """Find similar keywords using fuzzy matching"""
        if not keyword or not keyword_list:
            return []
        
        matches = process.extract(keyword, keyword_list, limit=5, scorer=fuzz.ratio)
        return [match[0] for match in matches if match[1] >= threshold]


class SuggestionsGenerator:
    """Main class for generating basic keyword suggestions"""
    
    def __init__(self):
        """Initialize the suggestions generator"""
        self.keyword_analyzer = KeywordAnalyzer()
        self.suggestion_templates = self._load_suggestion_templates()
    
    def _load_suggestion_templates(self) -> Dict[str, Dict]:
        """Load suggestion templates for different types"""
        return {
            'missing_keyword': {
                'title_template': "Add missing keyword: {keyword}",
                'description_template': "Consider adding '{keyword}' to your resume. This keyword appears in the job description but is missing from your resume.",
                'priority': SuggestionPriority.HIGH
            },
            'skill_enhancement': {
                'title_template': "Enhance {skill} section",
                'description_template': "Your {skill} skills could be better highlighted. Consider adding more specific examples or certifications.",
                'priority': SuggestionPriority.MEDIUM
            },
            'experience_improvement': {
                'title_template': "Improve experience description",
                'description_template': "Your experience section could benefit from more specific achievements and quantifiable results.",
                'priority': SuggestionPriority.MEDIUM
            },
            'format_optimization': {
                'title_template': "Optimize resume format",
                'description_template': "Consider restructuring your resume to better highlight relevant skills and experience.",
                'priority': SuggestionPriority.LOW
            }
        }
    
    def generate_suggestions(self, resume_keywords: Set[str], jd_keywords: Set[str], 
                           resume_text: str = "", jd_text: str = "") -> List[Dict]:
        """Generate comprehensive suggestions based on keyword analysis"""
        suggestions = []
        
        # 1. Missing keyword suggestions
        missing_keywords = jd_keywords - resume_keywords
        suggestions.extend(self._generate_missing_keyword_suggestions(missing_keywords, jd_text))
        
        # 2. Skill enhancement suggestions
        suggestions.extend(self._generate_skill_enhancement_suggestions(resume_keywords, jd_keywords))
        
        # 3. Experience improvement suggestions
        suggestions.extend(self._generate_experience_suggestions(resume_text, jd_text))
        
        # 4. Format optimization suggestions
        suggestions.extend(self._generate_format_suggestions(resume_text))
        
        # Sort by priority and confidence
        suggestions.sort(key=lambda x: (x['priority_score'], -x['confidence_score']), reverse=True)
        
        return suggestions[:10]  # Return top 10 suggestions
    
    def _generate_missing_keyword_suggestions(self, missing_keywords: Set[str], jd_text: str) -> List[Dict]:
        """Generate suggestions for missing keywords"""
        suggestions = []
        
        # Prioritize keywords by frequency in job description
        keyword_freq = Counter()
        for keyword in missing_keywords:
            keyword_freq[keyword] = jd_text.lower().count(keyword.lower())
        
        # Generate suggestions for top missing keywords
        for keyword, freq in keyword_freq.most_common(5):
            if len(keyword) > 2:  # Skip very short keywords
                suggestion = {
                    'type': SuggestionType.MISSING_KEYWORD.value,
                    'priority': SuggestionPriority.HIGH.value,
                    'priority_score': 3,
                    'title': f"Add missing keyword: {keyword}",
                    'description': f"The keyword '{keyword}' appears {freq} times in the job description but is missing from your resume. Consider incorporating this skill or technology into your experience descriptions.",
                    'missing_keywords': [keyword],
                    'suggested_keywords': [keyword],
                    'confidence_score': min(0.9, freq * 0.1),
                    'implementation_difficulty': 2,
                    'expected_impact': min(15.0, freq * 2.0)
                }
                suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_skill_enhancement_suggestions(self, resume_keywords: Set[str], jd_keywords: Set[str]) -> List[Dict]:
        """Generate suggestions for enhancing existing skills"""
        suggestions = []
        
        # Find overlapping skills that could be enhanced
        common_keywords = resume_keywords.intersection(jd_keywords)
        
        # Group related skills
        skill_groups = self._group_related_skills(common_keywords)
        
        for group_name, skills in skill_groups.items():
            if len(skills) >= 2:  # Only suggest for skill groups with multiple items
                suggestion = {
                    'type': SuggestionType.SKILL_ENHANCEMENT.value,
                    'priority': SuggestionPriority.MEDIUM.value,
                    'priority_score': 2,
                    'title': f"Enhance {group_name} skills section",
                    'description': f"You have experience with {', '.join(list(skills)[:3])}. Consider creating a dedicated section highlighting your {group_name} expertise with specific examples and achievements.",
                    'missing_keywords': [],
                    'suggested_keywords': list(skills),
                    'confidence_score': 0.7,
                    'implementation_difficulty': 3,
                    'expected_impact': 8.0
                }
                suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_experience_suggestions(self, resume_text: str, jd_text: str) -> List[Dict]:
        """Generate suggestions for improving experience descriptions"""
        suggestions = []
        
        # Analyze for quantifiable achievements
        has_numbers = bool(re.search(r'\d+%|\$\d+|\d+\+|increased|decreased|improved', resume_text.lower()))
        
        if not has_numbers:
            suggestion = {
                'type': SuggestionType.EXPERIENCE_IMPROVEMENT.value,
                'priority': SuggestionPriority.MEDIUM.value,
                'priority_score': 2,
                'title': "Add quantifiable achievements",
                'description': "Your resume would be stronger with specific, measurable achievements. Include percentages, dollar amounts, or other metrics that demonstrate your impact.",
                'missing_keywords': [],
                'suggested_keywords': ['metrics', 'achievements', 'results'],
                'confidence_score': 0.8,
                'implementation_difficulty': 4,
                'expected_impact': 12.0
            }
            suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_format_suggestions(self, resume_text: str) -> List[Dict]:
        """Generate suggestions for format optimization"""
        suggestions = []
        
        # Check for common format issues
        lines = resume_text.split('\n')
        
        # Check for bullet points
        has_bullets = any(line.strip().startswith(('•', '-', '*')) for line in lines)
        
        if not has_bullets:
            suggestion = {
                'type': SuggestionType.FORMAT_OPTIMIZATION.value,
                'priority': SuggestionPriority.LOW.value,
                'priority_score': 1,
                'title': "Use bullet points for better readability",
                'description': "Consider using bullet points to organize your experience and achievements. This makes your resume easier to scan and more ATS-friendly.",
                'missing_keywords': [],
                'suggested_keywords': ['formatting', 'bullet points', 'readability'],
                'confidence_score': 0.6,
                'implementation_difficulty': 2,
                'expected_impact': 5.0
            }
            suggestions.append(suggestion)
        
        return suggestions
    
    def _group_related_skills(self, keywords: Set[str]) -> Dict[str, Set[str]]:
        """Group related skills into categories"""
        skill_groups = defaultdict(set)
        
        # Define skill categories
        categories = {
            'Programming': ['python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust'],
            'Web Development': ['html', 'css', 'react', 'angular', 'vue', 'nodejs', 'express', 'django', 'flask'],
            'Database': ['sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch'],
            'Cloud': ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'terraform'],
            'Data Science': ['machine learning', 'data science', 'python', 'r', 'tensorflow', 'pytorch'],
            'Project Management': ['agile', 'scrum', 'kanban', 'jira', 'project management']
        }
        
        for keyword in keywords:
            for category, category_keywords in categories.items():
                if any(cat_keyword in keyword.lower() for cat_keyword in category_keywords):
                    skill_groups[category].add(keyword)
                    break
            else:
                skill_groups['Other'].add(keyword)
        
        # Remove empty groups and groups with only one item
        return {k: v for k, v in skill_groups.items() if len(v) > 1}
    
    def calculate_suggestion_metrics(self, suggestion: Dict, resume_keywords: Set[str], 
                                   jd_keywords: Set[str]) -> Dict:
        """Calculate additional metrics for a suggestion"""
        metrics = {
            'relevance_score': 0.0,
            'implementation_ease': 0.0,
            'impact_potential': 0.0
        }
        
        # Calculate relevance based on keyword frequency
        suggested_keywords = suggestion.get('suggested_keywords', [])
        if suggested_keywords:
            relevance_scores = []
            for keyword in suggested_keywords:
                if keyword in jd_keywords:
                    relevance_scores.append(1.0)
                else:
                    # Check for similar keywords
                    similar = self.keyword_analyzer.find_similar_keywords(keyword, list(jd_keywords))
                    relevance_scores.append(0.5 if similar else 0.1)
            
            metrics['relevance_score'] = np.mean(relevance_scores) if relevance_scores else 0.0
        
        # Implementation ease (inverse of difficulty)
        difficulty = suggestion.get('implementation_difficulty', 3)
        metrics['implementation_ease'] = (6 - difficulty) / 5.0
        
        # Impact potential
        expected_impact = suggestion.get('expected_impact', 0.0)
        metrics['impact_potential'] = min(1.0, expected_impact / 20.0)
        
        return metrics
