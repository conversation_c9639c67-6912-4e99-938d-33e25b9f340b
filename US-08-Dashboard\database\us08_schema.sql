-- US-08: Dashboard Database Schema
-- =================================
-- 
-- This file contains the PostgreSQL schema for the dashboard feature
-- in the Dr. Resume application. It includes tables for scan history,
-- analytics, user activity tracking, and export management.
-- 
-- Author: Dr. <PERSON>sume Development Team
-- Date: 2025-07-23
-- Version: 1.0.0

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for dashboard
CREATE TYPE scan_status AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed',
    'cancelled'
);

CREATE TYPE export_format AS ENUM (
    'pdf',
    'excel',
    'csv',
    'json'
);

-- Scan History Table
-- ==================
-- Comprehensive tracking of all resume scanning activities
CREATE TABLE IF NOT EXISTS scan_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User information
    user_id UUID NOT NULL,
    
    -- Related entities (from previous US)
    resume_id UUID NOT NULL,
    job_description_id UUID NOT NULL,
    matching_score_id UUID,
    
    -- Scan metadata
    scan_name VARCHAR(200) NOT NULL,
    scan_description TEXT,
    scan_status scan_status DEFAULT 'pending',
    
    -- Resume information
    resume_filename VARCHAR(255),
    resume_file_size INTEGER,
    resume_upload_date TIMESTAMP WITH TIME ZONE,
    
    -- Job description information
    job_title VARCHAR(200),
    company_name VARCHAR(200),
    job_location VARCHAR(200),
    job_type VARCHAR(50),
    
    -- Matching results
    overall_match_score DECIMAL(5,2) DEFAULT 0.0 CHECK (overall_match_score >= 0.0 AND overall_match_score <= 100.0),
    keyword_match_score DECIMAL(5,2) DEFAULT 0.0 CHECK (keyword_match_score >= 0.0 AND keyword_match_score <= 100.0),
    skill_match_score DECIMAL(5,2) DEFAULT 0.0 CHECK (skill_match_score >= 0.0 AND skill_match_score <= 100.0),
    experience_match_score DECIMAL(5,2) DEFAULT 0.0 CHECK (experience_match_score >= 0.0 AND experience_match_score <= 100.0),
    
    -- Keywords analysis
    total_keywords_found INTEGER DEFAULT 0,
    missing_keywords_count INTEGER DEFAULT 0,
    matched_keywords JSONB DEFAULT '[]'::jsonb,
    missing_keywords JSONB DEFAULT '[]'::jsonb,
    
    -- Suggestions summary
    total_suggestions INTEGER DEFAULT 0,
    high_priority_suggestions INTEGER DEFAULT 0,
    implemented_suggestions INTEGER DEFAULT 0,
    dismissed_suggestions INTEGER DEFAULT 0,
    
    -- Premium features usage
    has_premium_suggestions BOOLEAN DEFAULT FALSE,
    premium_cost DECIMAL(10,6) DEFAULT 0.0,
    ai_tokens_used INTEGER DEFAULT 0,
    
    -- Processing metrics
    processing_time_seconds DECIMAL(10,3) DEFAULT 0.0,
    processing_start_time TIMESTAMP WITH TIME ZONE,
    processing_end_time TIMESTAMP WITH TIME ZONE,
    
    -- User interaction tracking
    last_viewed_at TIMESTAMP WITH TIME ZONE,
    view_count INTEGER DEFAULT 0,
    is_bookmarked BOOLEAN DEFAULT FALSE,
    user_rating INTEGER CHECK (user_rating IS NULL OR (user_rating >= 1 AND user_rating <= 5)),
    user_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Dashboard Analytics Table
-- =========================
-- Pre-calculated analytics for improved dashboard performance
CREATE TABLE IF NOT EXISTS dashboard_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User information
    user_id UUID NOT NULL,
    
    -- Time period for analytics
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'yearly')),
    
    -- Scan statistics
    total_scans INTEGER DEFAULT 0,
    completed_scans INTEGER DEFAULT 0,
    failed_scans INTEGER DEFAULT 0,
    
    -- Score statistics
    average_match_score DECIMAL(5,2) DEFAULT 0.0,
    highest_match_score DECIMAL(5,2) DEFAULT 0.0,
    lowest_match_score DECIMAL(5,2) DEFAULT 0.0,
    score_improvement DECIMAL(5,2) DEFAULT 0.0,
    
    -- Keyword statistics
    total_keywords_analyzed INTEGER DEFAULT 0,
    average_missing_keywords DECIMAL(5,2) DEFAULT 0.0,
    most_common_missing_keywords JSONB DEFAULT '[]'::jsonb,
    
    -- Suggestion statistics
    total_suggestions_generated INTEGER DEFAULT 0,
    suggestions_implemented INTEGER DEFAULT 0,
    suggestions_dismissed INTEGER DEFAULT 0,
    implementation_rate DECIMAL(5,2) DEFAULT 0.0,
    
    -- Premium usage statistics
    premium_scans INTEGER DEFAULT 0,
    total_premium_cost DECIMAL(10,6) DEFAULT 0.0,
    ai_tokens_consumed INTEGER DEFAULT 0,
    
    -- Industry and job type analysis
    top_job_titles JSONB DEFAULT '[]'::jsonb,
    top_companies JSONB DEFAULT '[]'::jsonb,
    job_type_distribution JSONB DEFAULT '{}'::jsonb,
    
    -- Performance metrics
    average_processing_time DECIMAL(10,3) DEFAULT 0.0,
    total_processing_time DECIMAL(10,3) DEFAULT 0.0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT dashboard_analytics_user_period_unique UNIQUE (user_id, period_start, period_end, period_type)
);

-- User Activity Table
-- ===================
-- Track user activity and engagement for analytics
CREATE TABLE IF NOT EXISTS user_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User information
    user_id UUID NOT NULL,
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL,
    activity_description VARCHAR(200),
    
    -- Related entities
    related_scan_id UUID,
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    
    -- Activity metadata
    ip_address INET,
    user_agent VARCHAR(500),
    session_id VARCHAR(100),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Export History Table
-- ====================
-- Track export and report generation history
CREATE TABLE IF NOT EXISTS export_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- User information
    user_id UUID NOT NULL,
    
    -- Export details
    export_type VARCHAR(50) NOT NULL,
    export_format export_format NOT NULL,
    export_filename VARCHAR(255) NOT NULL,
    
    -- Export parameters
    date_range_start TIMESTAMP WITH TIME ZONE,
    date_range_end TIMESTAMP WITH TIME ZONE,
    filters_applied JSONB DEFAULT '{}'::jsonb,
    
    -- Export metadata
    file_size_bytes INTEGER,
    generation_time_seconds DECIMAL(10,3),
    download_count INTEGER DEFAULT 0,
    
    -- File storage
    file_path VARCHAR(500),
    is_available BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_downloaded_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for Performance
-- =======================

-- Scan history indexes
CREATE INDEX IF NOT EXISTS idx_scan_history_user_id ON scan_history(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_history_created_at ON scan_history(created_at);
CREATE INDEX IF NOT EXISTS idx_scan_history_status ON scan_history(scan_status);
CREATE INDEX IF NOT EXISTS idx_scan_history_user_created ON scan_history(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_scan_history_bookmarked ON scan_history(user_id, is_bookmarked) WHERE is_bookmarked = TRUE;
CREATE INDEX IF NOT EXISTS idx_scan_history_company ON scan_history(company_name);
CREATE INDEX IF NOT EXISTS idx_scan_history_job_title ON scan_history(job_title);
CREATE INDEX IF NOT EXISTS idx_scan_history_score ON scan_history(overall_match_score);

-- Dashboard analytics indexes
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_user_id ON dashboard_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_period ON dashboard_analytics(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_type ON dashboard_analytics(period_type);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_user_period ON dashboard_analytics(user_id, period_start, period_end);

-- User activity indexes
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON user_activity(created_at);
CREATE INDEX IF NOT EXISTS idx_user_activity_type ON user_activity(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_user_created ON user_activity(user_id, created_at DESC);

-- Export history indexes
CREATE INDEX IF NOT EXISTS idx_export_history_user_id ON export_history(user_id);
CREATE INDEX IF NOT EXISTS idx_export_history_created_at ON export_history(created_at);
CREATE INDEX IF NOT EXISTS idx_export_history_available ON export_history(is_available) WHERE is_available = TRUE;
CREATE INDEX IF NOT EXISTS idx_export_history_expires ON export_history(expires_at) WHERE expires_at IS NOT NULL;

-- JSON indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_scan_history_matched_keywords ON scan_history USING GIN (matched_keywords);
CREATE INDEX IF NOT EXISTS idx_scan_history_missing_keywords ON scan_history USING GIN (missing_keywords);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_job_titles ON dashboard_analytics USING GIN (top_job_titles);
CREATE INDEX IF NOT EXISTS idx_dashboard_analytics_companies ON dashboard_analytics USING GIN (top_companies);

-- Full-text search indexes
CREATE INDEX IF NOT EXISTS idx_scan_history_search ON scan_history USING GIN (
    to_tsvector('english', COALESCE(scan_name, '') || ' ' || COALESCE(scan_description, '') || ' ' || 
                COALESCE(job_title, '') || ' ' || COALESCE(company_name, ''))
);

-- Functions and Triggers
-- ======================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_scan_history_updated_at 
    BEFORE UPDATE ON scan_history 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dashboard_analytics_updated_at 
    BEFORE UPDATE ON dashboard_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically clean up expired exports
CREATE OR REPLACE FUNCTION cleanup_expired_exports()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    UPDATE export_history 
    SET is_available = FALSE 
    WHERE expires_at < CURRENT_TIMESTAMP AND is_available = TRUE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ language 'plpgsql';

-- Views for Common Queries
-- ========================

-- View for recent scan activity
CREATE OR REPLACE VIEW recent_scan_activity AS
SELECT 
    sh.*,
    CASE 
        WHEN sh.created_at > CURRENT_TIMESTAMP - INTERVAL '1 day' THEN 'today'
        WHEN sh.created_at > CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 'this_week'
        WHEN sh.created_at > CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 'this_month'
        ELSE 'older'
    END as recency,
    ROUND((sh.implemented_suggestions::DECIMAL / NULLIF(sh.total_suggestions, 0)) * 100, 2) as implementation_rate
FROM scan_history sh
WHERE sh.scan_status = 'completed'
ORDER BY sh.created_at DESC;

-- View for user dashboard summary
CREATE OR REPLACE VIEW user_dashboard_summary AS
SELECT 
    user_id,
    COUNT(*) as total_scans,
    COUNT(*) FILTER (WHERE scan_status = 'completed') as completed_scans,
    COUNT(*) FILTER (WHERE scan_status = 'failed') as failed_scans,
    ROUND(AVG(overall_match_score), 2) as average_score,
    MAX(overall_match_score) as highest_score,
    MIN(overall_match_score) as lowest_score,
    SUM(total_suggestions) as total_suggestions,
    SUM(implemented_suggestions) as implemented_suggestions,
    COUNT(*) FILTER (WHERE has_premium_suggestions = TRUE) as premium_scans,
    SUM(premium_cost) as total_premium_cost,
    MAX(created_at) as last_scan_date
FROM scan_history
GROUP BY user_id;

-- Sample Data for Development
-- ===========================

-- Note: Sample data will be inserted via the application's development endpoints
-- to ensure proper UUID generation and data consistency

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO dr_resume_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO dr_resume_user;

-- Comments for documentation
COMMENT ON TABLE scan_history IS 'Comprehensive tracking of all resume scanning activities and results';
COMMENT ON TABLE dashboard_analytics IS 'Pre-calculated analytics data for improved dashboard performance';
COMMENT ON TABLE user_activity IS 'User activity tracking for engagement analytics';
COMMENT ON TABLE export_history IS 'History of generated reports and exports';

COMMENT ON COLUMN scan_history.overall_match_score IS 'Overall matching score between resume and job description (0-100)';
COMMENT ON COLUMN scan_history.processing_time_seconds IS 'Time taken to process the scan in seconds';
COMMENT ON COLUMN scan_history.premium_cost IS 'Cost incurred for premium AI features in USD';

COMMENT ON COLUMN dashboard_analytics.score_improvement IS 'Percentage improvement compared to previous period';
COMMENT ON COLUMN dashboard_analytics.implementation_rate IS 'Percentage of suggestions that were implemented';

-- End of schema
