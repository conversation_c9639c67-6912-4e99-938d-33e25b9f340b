"""
US-08: Dashboard Analytics Service
==================================

This module provides analytics and data aggregation services for the dashboard feature.
It calculates metrics, generates insights, and prepares data for visualization.

Classes:
- AnalyticsService: Main service for analytics calculations
- MetricsCalculator: Helper class for metric calculations
- TrendAnalyzer: Helper class for trend analysis
- ReportGenerator: Service for generating reports

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, Counter
import statistics

# Data analysis libraries
import pandas as pd
import numpy as np
from sqlalchemy import func, and_, or_, desc, asc
from sqlalchemy.orm import sessionmaker

# Local imports
from us08_dashboard_model import db, ScanHistory, DashboardAnalytics, UserActivity, ScanStatus

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MetricsCalculator:
    """Helper class for calculating various dashboard metrics"""
    
    @staticmethod
    def calculate_score_improvement(current_scores: List[float], previous_scores: List[float]) -> float:
        """Calculate improvement percentage between two score sets"""
        if not current_scores or not previous_scores:
            return 0.0
        
        current_avg = statistics.mean(current_scores)
        previous_avg = statistics.mean(previous_scores)
        
        if previous_avg == 0:
            return 0.0
        
        return ((current_avg - previous_avg) / previous_avg) * 100
    
    @staticmethod
    def calculate_implementation_rate(total_suggestions: int, implemented: int) -> float:
        """Calculate suggestion implementation rate"""
        if total_suggestions == 0:
            return 0.0
        return (implemented / total_suggestions) * 100
    
    @staticmethod
    def calculate_success_rate(completed: int, total: int) -> float:
        """Calculate scan success rate"""
        if total == 0:
            return 0.0
        return (completed / total) * 100
    
    @staticmethod
    def get_percentile_scores(scores: List[float]) -> Dict[str, float]:
        """Calculate percentile scores"""
        if not scores:
            return {'p25': 0, 'p50': 0, 'p75': 0, 'p90': 0}
        
        return {
            'p25': np.percentile(scores, 25),
            'p50': np.percentile(scores, 50),  # median
            'p75': np.percentile(scores, 75),
            'p90': np.percentile(scores, 90)
        }


class TrendAnalyzer:
    """Helper class for analyzing trends in user data"""
    
    def __init__(self):
        self.metrics_calculator = MetricsCalculator()
    
    def analyze_score_trends(self, scan_history: List[ScanHistory], days: int = 30) -> Dict:
        """Analyze score trends over time"""
        if not scan_history:
            return {'trend': 'stable', 'change': 0.0, 'data_points': []}
        
        # Sort by date
        sorted_scans = sorted(scan_history, key=lambda x: x.created_at)
        
        # Group by week for trend analysis
        weekly_scores = defaultdict(list)
        for scan in sorted_scans:
            week_key = scan.created_at.strftime('%Y-W%U')
            weekly_scores[week_key].append(scan.overall_match_score)
        
        # Calculate weekly averages
        weekly_averages = []
        for week, scores in weekly_scores.items():
            if scores:
                weekly_averages.append(statistics.mean(scores))
        
        if len(weekly_averages) < 2:
            return {'trend': 'insufficient_data', 'change': 0.0, 'data_points': weekly_averages}
        
        # Calculate trend
        first_half = weekly_averages[:len(weekly_averages)//2]
        second_half = weekly_averages[len(weekly_averages)//2:]
        
        first_avg = statistics.mean(first_half)
        second_avg = statistics.mean(second_half)
        
        change = ((second_avg - first_avg) / first_avg * 100) if first_avg > 0 else 0
        
        if change > 5:
            trend = 'improving'
        elif change < -5:
            trend = 'declining'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'change': change,
            'data_points': weekly_averages,
            'first_period_avg': first_avg,
            'second_period_avg': second_avg
        }
    
    def analyze_keyword_trends(self, scan_history: List[ScanHistory]) -> Dict:
        """Analyze keyword trends and patterns"""
        all_missing_keywords = []
        all_matched_keywords = []
        
        for scan in scan_history:
            if scan.missing_keywords:
                all_missing_keywords.extend(scan.missing_keywords)
            if scan.matched_keywords:
                all_matched_keywords.extend(scan.matched_keywords)
        
        # Count frequency
        missing_counter = Counter(all_missing_keywords)
        matched_counter = Counter(all_matched_keywords)
        
        return {
            'most_common_missing': missing_counter.most_common(10),
            'most_common_matched': matched_counter.most_common(10),
            'total_unique_missing': len(missing_counter),
            'total_unique_matched': len(matched_counter),
            'improvement_opportunities': missing_counter.most_common(5)
        }
    
    def analyze_suggestion_patterns(self, scan_history: List[ScanHistory]) -> Dict:
        """Analyze suggestion implementation patterns"""
        total_suggestions = sum(scan.total_suggestions for scan in scan_history)
        total_implemented = sum(scan.implemented_suggestions for scan in scan_history)
        total_dismissed = sum(scan.dismissed_suggestions for scan in scan_history)
        
        implementation_rate = self.metrics_calculator.calculate_implementation_rate(
            total_suggestions, total_implemented
        )
        
        # Analyze by priority
        high_priority_total = sum(scan.high_priority_suggestions for scan in scan_history)
        
        return {
            'total_suggestions': total_suggestions,
            'total_implemented': total_implemented,
            'total_dismissed': total_dismissed,
            'implementation_rate': implementation_rate,
            'high_priority_suggestions': high_priority_total,
            'pending_suggestions': total_suggestions - total_implemented - total_dismissed
        }


class AnalyticsService:
    """Main service for dashboard analytics"""
    
    def __init__(self):
        self.metrics_calculator = MetricsCalculator()
        self.trend_analyzer = TrendAnalyzer()
    
    def get_user_overview(self, user_id: str, days: int = 30) -> Dict:
        """Get comprehensive user overview for dashboard"""
        try:
            # Get scan history for the period
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            scan_history = ScanHistory.query.filter(
                and_(
                    ScanHistory.user_id == user_id,
                    ScanHistory.created_at >= start_date,
                    ScanHistory.created_at <= end_date
                )
            ).order_by(desc(ScanHistory.created_at)).all()
            
            if not scan_history:
                return self._get_empty_overview()
            
            # Calculate basic metrics
            total_scans = len(scan_history)
            completed_scans = len([s for s in scan_history if s.scan_status == ScanStatus.COMPLETED])
            
            # Score analysis
            scores = [s.overall_match_score for s in scan_history if s.overall_match_score is not None]
            score_stats = self._calculate_score_statistics(scores)
            
            # Trend analysis
            score_trends = self.trend_analyzer.analyze_score_trends(scan_history, days)
            keyword_trends = self.trend_analyzer.analyze_keyword_trends(scan_history)
            suggestion_patterns = self.trend_analyzer.analyze_suggestion_patterns(scan_history)
            
            # Recent activity
            recent_scans = scan_history[:5]  # Last 5 scans
            
            # Premium usage
            premium_scans = len([s for s in scan_history if s.has_premium_suggestions])
            total_premium_cost = sum(s.premium_cost or 0 for s in scan_history)
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                },
                'summary': {
                    'total_scans': total_scans,
                    'completed_scans': completed_scans,
                    'success_rate': self.metrics_calculator.calculate_success_rate(completed_scans, total_scans),
                    'average_score': score_stats['average'],
                    'highest_score': score_stats['highest'],
                    'score_improvement': score_trends['change']
                },
                'scores': {
                    'statistics': score_stats,
                    'trends': score_trends,
                    'distribution': self._get_score_distribution(scores)
                },
                'keywords': keyword_trends,
                'suggestions': suggestion_patterns,
                'premium': {
                    'scans_count': premium_scans,
                    'total_cost': total_premium_cost,
                    'usage_rate': (premium_scans / total_scans * 100) if total_scans > 0 else 0
                },
                'recent_activity': [scan.to_dict() for scan in recent_scans],
                'performance': self._calculate_performance_metrics(scan_history)
            }
            
        except Exception as e:
            logger.error(f"Error generating user overview: {str(e)}")
            return self._get_empty_overview()
    
    def get_detailed_analytics(self, user_id: str, start_date: datetime, end_date: datetime) -> Dict:
        """Get detailed analytics for a specific date range"""
        try:
            scan_history = ScanHistory.query.filter(
                and_(
                    ScanHistory.user_id == user_id,
                    ScanHistory.created_at >= start_date,
                    ScanHistory.created_at <= end_date
                )
            ).all()
            
            # Time series data for charts
            daily_data = self._generate_daily_time_series(scan_history, start_date, end_date)
            
            # Job market analysis
            job_analysis = self._analyze_job_market_data(scan_history)
            
            # Skill gap analysis
            skill_gaps = self._analyze_skill_gaps(scan_history)
            
            # Performance benchmarks
            benchmarks = self._calculate_benchmarks(scan_history)
            
            return {
                'time_series': daily_data,
                'job_market': job_analysis,
                'skill_gaps': skill_gaps,
                'benchmarks': benchmarks,
                'recommendations': self._generate_recommendations(scan_history)
            }
            
        except Exception as e:
            logger.error(f"Error generating detailed analytics: {str(e)}")
            return {}
    
    def _calculate_score_statistics(self, scores: List[float]) -> Dict:
        """Calculate comprehensive score statistics"""
        if not scores:
            return {'average': 0, 'highest': 0, 'lowest': 0, 'median': 0, 'std_dev': 0}
        
        return {
            'average': round(statistics.mean(scores), 2),
            'highest': round(max(scores), 2),
            'lowest': round(min(scores), 2),
            'median': round(statistics.median(scores), 2),
            'std_dev': round(statistics.stdev(scores) if len(scores) > 1 else 0, 2),
            'percentiles': self.metrics_calculator.get_percentile_scores(scores)
        }
    
    def _get_score_distribution(self, scores: List[float]) -> Dict:
        """Get score distribution for visualization"""
        if not scores:
            return {'ranges': {}, 'total': 0}
        
        ranges = {
            '0-20': 0, '21-40': 0, '41-60': 0, '61-80': 0, '81-100': 0
        }
        
        for score in scores:
            if score <= 20:
                ranges['0-20'] += 1
            elif score <= 40:
                ranges['21-40'] += 1
            elif score <= 60:
                ranges['41-60'] += 1
            elif score <= 80:
                ranges['61-80'] += 1
            else:
                ranges['81-100'] += 1
        
        return {'ranges': ranges, 'total': len(scores)}
    
    def _calculate_performance_metrics(self, scan_history: List[ScanHistory]) -> Dict:
        """Calculate performance metrics"""
        if not scan_history:
            return {'average_processing_time': 0, 'total_processing_time': 0}
        
        processing_times = [s.processing_time_seconds for s in scan_history if s.processing_time_seconds]
        
        return {
            'average_processing_time': round(statistics.mean(processing_times), 2) if processing_times else 0,
            'total_processing_time': round(sum(processing_times), 2) if processing_times else 0,
            'fastest_scan': round(min(processing_times), 2) if processing_times else 0,
            'slowest_scan': round(max(processing_times), 2) if processing_times else 0
        }
    
    def _generate_daily_time_series(self, scan_history: List[ScanHistory], 
                                   start_date: datetime, end_date: datetime) -> Dict:
        """Generate daily time series data for charts"""
        daily_data = defaultdict(lambda: {'scans': 0, 'avg_score': 0, 'scores': []})
        
        for scan in scan_history:
            date_key = scan.created_at.strftime('%Y-%m-%d')
            daily_data[date_key]['scans'] += 1
            if scan.overall_match_score is not None:
                daily_data[date_key]['scores'].append(scan.overall_match_score)
        
        # Calculate averages and format for frontend
        result = []
        current_date = start_date
        while current_date <= end_date:
            date_key = current_date.strftime('%Y-%m-%d')
            data = daily_data[date_key]
            
            avg_score = statistics.mean(data['scores']) if data['scores'] else 0
            
            result.append({
                'date': date_key,
                'scans': data['scans'],
                'average_score': round(avg_score, 2)
            })
            
            current_date += timedelta(days=1)
        
        return {'daily': result}
    
    def _analyze_job_market_data(self, scan_history: List[ScanHistory]) -> Dict:
        """Analyze job market trends from scan data"""
        job_titles = [s.job_title for s in scan_history if s.job_title]
        companies = [s.company_name for s in scan_history if s.company_name]
        locations = [s.job_location for s in scan_history if s.job_location]
        job_types = [s.job_type for s in scan_history if s.job_type]
        
        return {
            'top_job_titles': Counter(job_titles).most_common(10),
            'top_companies': Counter(companies).most_common(10),
            'top_locations': Counter(locations).most_common(10),
            'job_type_distribution': dict(Counter(job_types))
        }
    
    def _analyze_skill_gaps(self, scan_history: List[ScanHistory]) -> Dict:
        """Analyze skill gaps across all scans"""
        all_missing = []
        all_matched = []
        
        for scan in scan_history:
            if scan.missing_keywords:
                all_missing.extend(scan.missing_keywords)
            if scan.matched_keywords:
                all_matched.extend(scan.matched_keywords)
        
        missing_counter = Counter(all_missing)
        matched_counter = Counter(all_matched)
        
        # Calculate skill coverage
        total_skills = len(set(all_missing + all_matched))
        matched_skills = len(set(all_matched))
        coverage_rate = (matched_skills / total_skills * 100) if total_skills > 0 else 0
        
        return {
            'most_needed_skills': missing_counter.most_common(15),
            'strongest_skills': matched_counter.most_common(15),
            'skill_coverage_rate': round(coverage_rate, 2),
            'total_unique_skills': total_skills,
            'improvement_priority': missing_counter.most_common(5)
        }
    
    def _calculate_benchmarks(self, scan_history: List[ScanHistory]) -> Dict:
        """Calculate performance benchmarks"""
        if not scan_history:
            return {}
        
        scores = [s.overall_match_score for s in scan_history if s.overall_match_score is not None]
        
        if not scores:
            return {}
        
        # Industry benchmarks (mock data - in real app, this would come from aggregated data)
        industry_avg = 65.0  # Mock industry average
        top_performer_threshold = 85.0
        
        user_avg = statistics.mean(scores)
        
        return {
            'user_average': round(user_avg, 2),
            'industry_average': industry_avg,
            'performance_vs_industry': round(user_avg - industry_avg, 2),
            'top_performer_threshold': top_performer_threshold,
            'is_top_performer': user_avg >= top_performer_threshold,
            'improvement_needed': max(0, round(top_performer_threshold - user_avg, 2))
        }
    
    def _generate_recommendations(self, scan_history: List[ScanHistory]) -> List[Dict]:
        """Generate actionable recommendations based on analytics"""
        recommendations = []
        
        if not scan_history:
            return recommendations
        
        # Analyze patterns and generate recommendations
        scores = [s.overall_match_score for s in scan_history if s.overall_match_score is not None]
        
        if scores:
            avg_score = statistics.mean(scores)
            
            if avg_score < 50:
                recommendations.append({
                    'type': 'score_improvement',
                    'priority': 'high',
                    'title': 'Focus on Basic Resume Optimization',
                    'description': 'Your average matching score is below 50%. Focus on adding missing keywords and improving resume structure.',
                    'action': 'Review and implement high-priority suggestions from your recent scans.'
                })
            
            elif avg_score < 75:
                recommendations.append({
                    'type': 'skill_enhancement',
                    'priority': 'medium',
                    'title': 'Enhance Skill Presentation',
                    'description': 'Your scores show room for improvement. Focus on better presenting your existing skills.',
                    'action': 'Use premium suggestions to get detailed improvement recommendations.'
                })
        
        # Add more recommendation logic based on other patterns
        
        return recommendations
    
    def _get_empty_overview(self) -> Dict:
        """Return empty overview structure"""
        return {
            'period': {'start_date': None, 'end_date': None, 'days': 0},
            'summary': {'total_scans': 0, 'completed_scans': 0, 'success_rate': 0, 'average_score': 0},
            'scores': {'statistics': {}, 'trends': {}, 'distribution': {}},
            'keywords': {},
            'suggestions': {},
            'premium': {'scans_count': 0, 'total_cost': 0, 'usage_rate': 0},
            'recent_activity': [],
            'performance': {}
        }
