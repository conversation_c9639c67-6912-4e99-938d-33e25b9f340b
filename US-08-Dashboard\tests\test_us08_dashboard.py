"""
US-08: Dashboard Test Suite
===========================

Comprehensive test suite for the dashboard feature in the Dr. Resume application.
Tests analytics, scan history, metrics calculation, and data visualization.

Test Categories:
- Unit tests for analytics service
- API endpoint tests
- Database model tests
- Metrics calculation tests
- Data aggregation tests

Author: Dr. <PERSON>sume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import pytest
import json
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Flask and testing imports
from flask import Flask
from flask_testing import TestCase

# Local imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from us08_app import create_app
from us08_dashboard_model import db, ScanHistory, DashboardAnalytics, UserActivity, ExportHistory, ScanStatus
from us08_analytics_service import AnalyticsService, MetricsCalculator, TrendAnalyzer


class DashboardTestCase(TestCase):
    """Base test case for dashboard tests"""
    
    def create_app(self):
        """Create test Flask application"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        return app
    
    def setUp(self):
        """Set up test database and sample data"""
        db.create_all()
        self.create_sample_data()
    
    def tearDown(self):
        """Clean up test database"""
        db.session.remove()
        db.drop_all()
    
    def create_sample_data(self):
        """Create sample data for testing"""
        self.sample_user_id = str(uuid.uuid4())
        
        # Create sample scan history
        self.sample_scans = []
        for i in range(5):
            scan = ScanHistory(
                user_id=self.sample_user_id,
                resume_id=str(uuid.uuid4()),
                job_description_id=str(uuid.uuid4()),
                scan_name=f"Test Scan {i+1}",
                scan_description=f"Test description {i+1}",
                scan_status=ScanStatus.COMPLETED,
                job_title="Software Developer",
                company_name=f"Company {i+1}",
                overall_match_score=60.0 + (i * 5),
                total_suggestions=10 - i,
                implemented_suggestions=i + 1,
                has_premium_suggestions=i % 2 == 0,
                created_at=datetime.utcnow() - timedelta(days=i)
            )
            self.sample_scans.append(scan)
            db.session.add(scan)
        
        db.session.commit()
    
    def get_auth_headers(self):
        """Get authentication headers for testing"""
        return {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
        }


class TestAnalyticsService(DashboardTestCase):
    """Test the analytics service"""
    
    def setUp(self):
        super().setUp()
        self.analytics_service = AnalyticsService()
    
    def test_analytics_service_initialization(self):
        """Test analytics service initialization"""
        self.assertIsNotNone(self.analytics_service)
        self.assertIsNotNone(self.analytics_service.metrics_calculator)
        self.assertIsNotNone(self.analytics_service.trend_analyzer)
    
    def test_get_user_overview(self):
        """Test getting user overview"""
        overview = self.analytics_service.get_user_overview(self.sample_user_id, 30)
        
        self.assertIsInstance(overview, dict)
        self.assertIn('summary', overview)
        self.assertIn('scores', overview)
        self.assertIn('suggestions', overview)
        self.assertIn('recent_activity', overview)
        
        # Check summary data
        summary = overview['summary']
        self.assertEqual(summary['total_scans'], 5)
        self.assertEqual(summary['completed_scans'], 5)
        self.assertGreater(summary['average_score'], 0)
    
    def test_get_detailed_analytics(self):
        """Test getting detailed analytics"""
        start_date = datetime.utcnow() - timedelta(days=30)
        end_date = datetime.utcnow()
        
        analytics = self.analytics_service.get_detailed_analytics(
            self.sample_user_id, start_date, end_date
        )
        
        self.assertIsInstance(analytics, dict)
        # Note: Detailed analytics might be empty for test data
    
    def test_empty_user_overview(self):
        """Test user overview with no data"""
        empty_user_id = str(uuid.uuid4())
        overview = self.analytics_service.get_user_overview(empty_user_id, 30)
        
        self.assertIsInstance(overview, dict)
        self.assertEqual(overview['summary']['total_scans'], 0)


class TestMetricsCalculator(DashboardTestCase):
    """Test the metrics calculator"""
    
    def setUp(self):
        super().setUp()
        self.calculator = MetricsCalculator()
    
    def test_calculate_score_improvement(self):
        """Test score improvement calculation"""
        current_scores = [80, 85, 90]
        previous_scores = [70, 75, 80]
        
        improvement = self.calculator.calculate_score_improvement(current_scores, previous_scores)
        
        self.assertIsInstance(improvement, float)
        self.assertGreater(improvement, 0)  # Should show improvement
    
    def test_calculate_implementation_rate(self):
        """Test implementation rate calculation"""
        total_suggestions = 10
        implemented = 7
        
        rate = self.calculator.calculate_implementation_rate(total_suggestions, implemented)
        
        self.assertEqual(rate, 70.0)
    
    def test_calculate_success_rate(self):
        """Test success rate calculation"""
        completed = 8
        total = 10
        
        rate = self.calculator.calculate_success_rate(completed, total)
        
        self.assertEqual(rate, 80.0)
    
    def test_get_percentile_scores(self):
        """Test percentile score calculation"""
        scores = [60, 65, 70, 75, 80, 85, 90, 95]
        
        percentiles = self.calculator.get_percentile_scores(scores)
        
        self.assertIn('p25', percentiles)
        self.assertIn('p50', percentiles)
        self.assertIn('p75', percentiles)
        self.assertIn('p90', percentiles)
        
        # Check that percentiles are in ascending order
        self.assertLessEqual(percentiles['p25'], percentiles['p50'])
        self.assertLessEqual(percentiles['p50'], percentiles['p75'])
        self.assertLessEqual(percentiles['p75'], percentiles['p90'])


class TestTrendAnalyzer(DashboardTestCase):
    """Test the trend analyzer"""
    
    def setUp(self):
        super().setUp()
        self.trend_analyzer = TrendAnalyzer()
    
    def test_analyze_score_trends(self):
        """Test score trend analysis"""
        trends = self.trend_analyzer.analyze_score_trends(self.sample_scans, 30)
        
        self.assertIsInstance(trends, dict)
        self.assertIn('trend', trends)
        self.assertIn('change', trends)
        self.assertIn('data_points', trends)
        
        # Trend should be one of the expected values
        self.assertIn(trends['trend'], ['improving', 'declining', 'stable', 'insufficient_data'])
    
    def test_analyze_keyword_trends(self):
        """Test keyword trend analysis"""
        # Add some keyword data to sample scans
        for i, scan in enumerate(self.sample_scans):
            scan.missing_keywords = [f'keyword{i}', 'python', 'javascript']
            scan.matched_keywords = ['html', 'css', f'skill{i}']
        
        db.session.commit()
        
        trends = self.trend_analyzer.analyze_keyword_trends(self.sample_scans)
        
        self.assertIsInstance(trends, dict)
        self.assertIn('most_common_missing', trends)
        self.assertIn('most_common_matched', trends)
        self.assertIn('improvement_opportunities', trends)
    
    def test_analyze_suggestion_patterns(self):
        """Test suggestion pattern analysis"""
        patterns = self.trend_analyzer.analyze_suggestion_patterns(self.sample_scans)
        
        self.assertIsInstance(patterns, dict)
        self.assertIn('total_suggestions', patterns)
        self.assertIn('total_implemented', patterns)
        self.assertIn('implementation_rate', patterns)
        
        # Check calculated values
        expected_total = sum(scan.total_suggestions for scan in self.sample_scans)
        self.assertEqual(patterns['total_suggestions'], expected_total)


class TestDashboardAPI(DashboardTestCase):
    """Test the dashboard API endpoints"""
    
    @patch('us08_dashboard_routes.get_jwt_identity')
    def test_get_dashboard_overview(self, mock_jwt_identity):
        """Test dashboard overview endpoint"""
        mock_jwt_identity.return_value = self.sample_user_id
        
        response = self.client.get(
            '/api/dashboard/overview',
            headers=self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('data', data)
    
    @patch('us08_dashboard_routes.get_jwt_identity')
    def test_get_scan_history(self, mock_jwt_identity):
        """Test scan history endpoint"""
        mock_jwt_identity.return_value = self.sample_user_id
        
        response = self.client.get(
            '/api/dashboard/history',
            headers=self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('scans', data['data'])
        self.assertIn('pagination', data['data'])
    
    @patch('us08_dashboard_routes.get_jwt_identity')
    def test_get_scan_history_with_filters(self, mock_jwt_identity):
        """Test scan history with filters"""
        mock_jwt_identity.return_value = self.sample_user_id
        
        response = self.client.get(
            '/api/dashboard/history?status=completed&page=1&per_page=5',
            headers=self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    @patch('us08_dashboard_routes.get_jwt_identity')
    def test_get_detailed_analytics(self, mock_jwt_identity):
        """Test detailed analytics endpoint"""
        mock_jwt_identity.return_value = self.sample_user_id
        
        start_date = (datetime.utcnow() - timedelta(days=30)).isoformat()
        end_date = datetime.utcnow().isoformat()
        
        response = self.client.get(
            f'/api/dashboard/analytics?start_date={start_date}&end_date={end_date}',
            headers=self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    @patch('us08_dashboard_routes.get_jwt_identity')
    def test_update_scan_details(self, mock_jwt_identity):
        """Test updating scan details"""
        mock_jwt_identity.return_value = self.sample_user_id
        
        scan_id = str(self.sample_scans[0].id)
        update_data = {
            'scan_name': 'Updated Scan Name',
            'is_bookmarked': True,
            'user_rating': 5
        }
        
        response = self.client.put(
            f'/api/dashboard/scan/{scan_id}',
            headers=self.get_auth_headers(),
            data=json.dumps(update_data)
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verify database update
        updated_scan = ScanHistory.query.get(scan_id)
        self.assertEqual(updated_scan.scan_name, 'Updated Scan Name')
        self.assertTrue(updated_scan.is_bookmarked)
        self.assertEqual(updated_scan.user_rating, 5)


class TestDashboardModels(DashboardTestCase):
    """Test the dashboard database models"""
    
    def test_scan_history_model_creation(self):
        """Test creating a scan history model"""
        scan = ScanHistory(
            user_id=str(uuid.uuid4()),
            resume_id=str(uuid.uuid4()),
            job_description_id=str(uuid.uuid4()),
            scan_name="Test Scan",
            scan_description="Test Description",
            job_title="Developer",
            company_name="Test Company",
            overall_match_score=75.5
        )
        
        db.session.add(scan)
        db.session.commit()
        
        self.assertIsNotNone(scan.id)
        self.assertEqual(scan.scan_name, "Test Scan")
        self.assertEqual(scan.overall_match_score, 75.5)
    
    def test_scan_history_to_dict(self):
        """Test scan history serialization"""
        scan = self.sample_scans[0]
        scan_dict = scan.to_dict()
        
        self.assertIsInstance(scan_dict, dict)
        self.assertIn('id', scan_dict)
        self.assertIn('scan_name', scan_dict)
        self.assertIn('overall_match_score', scan_dict)
        self.assertIn('created_at', scan_dict)
    
    def test_scan_history_improvement_percentage(self):
        """Test improvement percentage calculation"""
        scan = self.sample_scans[0]
        scan.total_suggestions = 10
        scan.implemented_suggestions = 7
        
        improvement = scan.get_improvement_percentage()
        self.assertEqual(improvement, 70.0)
    
    def test_user_activity_model(self):
        """Test user activity model"""
        activity = UserActivity(
            user_id=self.sample_user_id,
            activity_type='dashboard_view',
            activity_description='Viewed dashboard overview',
            ip_address='***********'
        )
        
        db.session.add(activity)
        db.session.commit()
        
        self.assertIsNotNone(activity.id)
        self.assertEqual(activity.activity_type, 'dashboard_view')
    
    def test_dashboard_analytics_model(self):
        """Test dashboard analytics model"""
        analytics = DashboardAnalytics(
            user_id=self.sample_user_id,
            period_start=datetime.utcnow() - timedelta(days=7),
            period_end=datetime.utcnow(),
            period_type='weekly',
            total_scans=5,
            average_match_score=75.0
        )
        
        db.session.add(analytics)
        db.session.commit()
        
        self.assertIsNotNone(analytics.id)
        self.assertEqual(analytics.period_type, 'weekly')
        self.assertEqual(analytics.total_scans, 5)


class TestHealthEndpoints(DashboardTestCase):
    """Test health check endpoints"""
    
    def test_health_endpoint(self):
        """Test main health endpoint"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('version', data)
        self.assertIn('features', data)
    
    def test_api_health_endpoint(self):
        """Test API health endpoint"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('statistics', data)
        self.assertIn('endpoints', data)


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
