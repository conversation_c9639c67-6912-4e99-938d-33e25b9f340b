# US-05 & US-06 Integration Guide

## 📋 Overview

This guide explains how **US-05: Keyword Parsing** and **US-06: Matching Score** work together to create an intelligent resume-job description matching system. US-05 extracts keywords using NLP, and US-06 uses those keywords to calculate similarity scores with visual progress bars.

## 🔄 Complete Data Flow

```
User Upload → Text Extraction → Keyword Parsing → Matching Calculation → Visual Display
    ↓              ↓                ↓                    ↓                  ↓
  US-03/04      US-03/04          US-05               US-06             US-06
```

### Detailed Flow

1. **Document Upload** (US-03/US-04):
   - User uploads resume or job description
   - Text is extracted and stored in database
   - Document marked as uploaded

2. **Automatic Keyword Extraction** (US-05):
   - Triggered immediately after successful upload
   - NLP processing extracts keywords using spaCy/NLTK
   - Keywords classified by type and stored with confidence scores
   - Document marked as `keywords_extracted = true`

3. **Matching Calculation** (US-06):
   - User selects resume and job description pair
   - System retrieves keywords for both documents
   - Jaccard similarity and other algorithms calculate match percentage
   - Results stored in matching_scores table

4. **Visual Display** (US-06):
   - Color-coded progress bars show overall and category-specific matches
   - Keyword analysis shows matched, missing, and extra keywords
   - Real-time updates with smooth animations

## 🔗 Database Relationships

```sql
-- US-05: Keywords table
keywords (
    id,
    user_id → users.id,
    resume_id → resumes.id,
    job_description_id → job_descriptions.id,
    keyword,
    keyword_type,
    confidence_score
)

-- US-06: Matching scores table  
matching_scores (
    id,
    user_id → users.id,
    resume_id → resumes.id,
    job_description_id → job_descriptions.id,
    overall_match_percentage,
    jaccard_similarity,
    matched_keywords,
    missing_keywords
)
```

## 🧠 Algorithm Integration

### Step 1: Keyword Extraction (US-05)

**Resume Processing**:
```python
# US-05: Extract keywords from resume
resume_text = "Python developer with 5 years Flask experience"
extractor = KeywordExtractor(method='hybrid')
keywords = extractor.extract_keywords(resume_text, 'resume')

# Results:
[
    {'keyword': 'python', 'keyword_type': 'technology', 'confidence': 0.95},
    {'keyword': 'flask', 'keyword_type': 'framework', 'confidence': 0.90},
    {'keyword': '5 years', 'keyword_type': 'experience', 'confidence': 0.85},
    {'keyword': 'developer', 'keyword_type': 'experience', 'confidence': 0.80}
]
```

**Job Description Processing**:
```python
# US-05: Extract keywords from job description
jd_text = "Senior Python developer needed. Flask and Django experience required."
keywords = extractor.extract_keywords(jd_text, 'job_description')

# Results:
[
    {'keyword': 'python', 'keyword_type': 'technology', 'confidence': 0.98},
    {'keyword': 'flask', 'keyword_type': 'framework', 'confidence': 0.95},
    {'keyword': 'django', 'keyword_type': 'framework', 'confidence': 0.95},
    {'keyword': 'senior', 'keyword_type': 'experience', 'confidence': 0.90}
]
```

### Step 2: Matching Calculation (US-06)

**Retrieve Keywords**:
```python
# US-06: Get keywords for both documents
resume_keywords = Keyword.get_by_document(user_id, resume_id=resume_id)
jd_keywords = Keyword.get_by_document(user_id, job_description_id=jd_id)
```

**Calculate Jaccard Similarity**:
```python
# US-06: Calculate similarity
resume_set = {'python', 'flask', '5 years', 'developer'}
jd_set = {'python', 'flask', 'django', 'senior'}

intersection = resume_set & jd_set  # {'python', 'flask'}
union = resume_set | jd_set         # {'python', 'flask', '5 years', 'developer', 'django', 'senior'}

jaccard_similarity = len(intersection) / len(union)  # 2/6 = 0.333 (33.3%)
```

**Generate Analysis**:
```python
# US-06: Keyword analysis
analysis = {
    'matched_keywords': ['python', 'flask'],           # Common keywords
    'missing_keywords': ['django', 'senior'],          # In JD but not resume
    'extra_keywords': ['5 years', 'developer'],        # In resume but not JD
    'overall_match_percentage': 33.3,
    'match_category': 'poor'  # <40% is poor
}
```

## 🎨 Frontend Integration

### Progress Bar Display

**HTML Structure** (US-06):
```html
<!-- Overall Match Progress Bar -->
<div class="progress-container">
    <div class="d-flex justify-content-between">
        <span><span class="match-indicator poor"></span>Overall Match</span>
        <span>33%</span>
    </div>
    <div class="progress">
        <div class="progress-bar poor" style="width: 33%">33%</div>
    </div>
    <div class="help-text">Poor Match (20-39%)</div>
</div>
```

**JavaScript Updates** (US-06):
```javascript
// Update progress bar with US-06 data
function displayMatchingResults(matchingScore) {
    updateProgressBar('overall', matchingScore.overall_match_percentage, 
                     matchingScore.match_category);
    
    updateKeywordAnalysis(matchingScore.matched_keywords, 
                         matchingScore.missing_keywords,
                         matchingScore.extra_keywords);
}
```

## 🔧 API Integration

### US-05 to US-06 Data Flow

**1. Extract Keywords (US-05)**:
```bash
POST /api/extract_keywords
{
    "resume_id": "uuid-here",
    "extraction_method": "hybrid"
}
```

**2. Calculate Match (US-06)**:
```bash
POST /api/calculate_match
{
    "resume_id": "uuid-here", 
    "job_description_id": "uuid-here",
    "calculation_method": "jaccard"
}
```

**3. Get Results (US-06)**:
```bash
GET /api/matching_scores
```

### Automatic Processing Integration

**US-03 Resume Upload Integration**:
```python
# In us03_upload_routes.py
from us05_auto_processor import auto_process_resume

@resume_bp.route('/upload', methods=['POST'])
def upload_resume():
    # ... existing upload logic ...
    
    if upload_successful:
        # Trigger automatic keyword extraction
        success, message, count = auto_process_resume(resume.id, user.id)
        if success:
            print(f"Extracted {count} keywords automatically")
        
        return jsonify({
            'success': True,
            'resume_id': resume.id,
            'keywords_extracted': success,
            'keyword_count': count
        })
```

**US-04 Job Description Upload Integration**:
```python
# In us04_upload_routes.py  
from us05_auto_processor import auto_process_job_description

@jd_bp.route('/upload', methods=['POST'])
def upload_job_description():
    # ... existing upload logic ...
    
    if upload_successful:
        # Trigger automatic keyword extraction
        success, message, count = auto_process_job_description(jd.id, user.id)
        
        return jsonify({
            'success': True,
            'job_description_id': jd.id,
            'keywords_extracted': success,
            'keyword_count': count
        })
```

## 📊 Performance Optimization

### Caching Strategy

**US-05 Keyword Caching**:
```python
# Cache extracted keywords to avoid reprocessing
@lru_cache(maxsize=1000)
def get_cached_keywords(user_id, document_id, document_type):
    return Keyword.get_by_document(user_id, 
                                  resume_id=document_id if document_type=='resume' else None,
                                  job_description_id=document_id if document_type=='jd' else None)
```

**US-06 Matching Caching**:
```python
# Cache matching calculations for identical keyword sets
@lru_cache(maxsize=500)
def get_cached_similarity(resume_keywords_hash, jd_keywords_hash):
    # Calculate similarity only if not cached
    return calculate_jaccard_similarity(resume_keywords, jd_keywords)
```

### Database Optimization

**Efficient Queries**:
```sql
-- US-05: Get keywords with single query
SELECT keyword, keyword_type, confidence_score 
FROM keywords 
WHERE user_id = ? AND resume_id = ?
ORDER BY confidence_score DESC;

-- US-06: Get matching score with document info
SELECT ms.*, r.resume_title, jd.title as job_title
FROM matching_scores ms
JOIN resumes r ON ms.resume_id = r.id  
JOIN job_descriptions jd ON ms.job_description_id = jd.id
WHERE ms.user_id = ? AND ms.is_current = true;
```

## 🧪 Integration Testing

### End-to-End Test Scenario

```python
def test_complete_matching_workflow(client, auth_user):
    """Test complete workflow from upload to matching"""
    
    # Step 1: Upload resume (US-03)
    resume_data = {'file': 'test_resume.pdf'}
    response = client.post('/api/upload_resume', data=resume_data,
                          headers={'Authorization': f'Bearer {auth_user}'})
    resume_id = response.json['resume_id']
    
    # Step 2: Upload job description (US-04)  
    jd_data = {'title': 'Python Developer', 'description': 'Python and Flask required'}
    response = client.post('/api/upload_job_description', json=jd_data,
                          headers={'Authorization': f'Bearer {auth_user}'})
    jd_id = response.json['job_description_id']
    
    # Step 3: Verify keywords extracted (US-05)
    response = client.get(f'/api/keywords?resume_id={resume_id}',
                         headers={'Authorization': f'Bearer {auth_user}'})
    assert len(response.json['keywords']) > 0
    
    # Step 4: Calculate matching score (US-06)
    match_data = {'resume_id': resume_id, 'job_description_id': jd_id}
    response = client.post('/api/calculate_match', json=match_data,
                          headers={'Authorization': f'Bearer {auth_user}'})
    
    # Step 5: Verify results
    assert response.status_code == 200
    assert 'matching_score' in response.json
    assert 'overall_match_percentage' in response.json['matching_score']
```

## 🚨 Common Integration Issues

### Issue 1: Keywords Not Found for Matching
**Problem**: US-06 fails because US-05 hasn't processed documents
**Solution**:
```python
# In US-06 matching calculator
def calculate_overall_match(self, resume_id, jd_id, user_id):
    resume_keywords = self.get_keywords_by_document(user_id, resume_id=resume_id)
    jd_keywords = self.get_keywords_by_document(user_id, job_description_id=jd_id)
    
    if not resume_keywords:
        return None, "Resume keywords not found. Please run keyword extraction first."
    
    if not jd_keywords:
        return None, "Job description keywords not found. Please run keyword extraction first."
```

### Issue 2: Inconsistent Keyword Types
**Problem**: US-05 and US-06 use different keyword type classifications
**Solution**:
```python
# Standardize keyword types across both US
STANDARD_KEYWORD_TYPES = [
    'technology', 'framework', 'skill', 'experience', 
    'education', 'certification', 'tool', 'language', 'general'
]

# Use in both US-05 extraction and US-06 matching
def normalize_keyword_type(keyword_type):
    return keyword_type if keyword_type in STANDARD_KEYWORD_TYPES else 'general'
```

### Issue 3: Performance Issues with Large Documents
**Problem**: Slow processing when documents have many keywords
**Solution**:
```python
# Implement pagination and limits
def get_keywords_for_matching(user_id, document_id, document_type, limit=100):
    """Get top keywords for matching to avoid performance issues"""
    return Keyword.get_by_document(user_id, 
                                  resume_id=document_id if document_type=='resume' else None,
                                  job_description_id=document_id if document_type=='jd' else None)\
                  .order_by(Keyword.confidence_score.desc())\
                  .limit(limit)
```

## 🎯 Best Practices

### 1. Error Handling
```python
# US-05: Graceful keyword extraction failure
try:
    keywords = extractor.extract_keywords(text, source_type)
    if not keywords:
        # Mark as processed even if no keywords found
        document.keywords_extracted = True
        document.processing_notes = "No keywords extracted"
except Exception as e:
    document.processing_notes = f"Extraction failed: {str(e)}"
    # Don't mark as processed so it can be retried
```

### 2. Data Consistency
```python
# US-06: Ensure data consistency before matching
def validate_matching_prerequisites(resume_id, jd_id, user_id):
    resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
    jd = JobDescription.query.filter_by(id=jd_id, user_id=user_id).first()
    
    if not resume or not resume.keywords_extracted:
        return False, "Resume not processed"
    
    if not jd or not jd.keywords_extracted:
        return False, "Job description not processed"
    
    return True, "Ready for matching"
```

### 3. User Feedback
```python
# Provide clear status updates to users
def get_processing_status(user_id):
    return {
        'resumes': {
            'total': Resume.query.filter_by(user_id=user_id).count(),
            'processed': Resume.query.filter_by(user_id=user_id, keywords_extracted=True).count()
        },
        'job_descriptions': {
            'total': JobDescription.query.filter_by(user_id=user_id).count(), 
            'processed': JobDescription.query.filter_by(user_id=user_id, keywords_extracted=True).count()
        },
        'matching_scores': MatchingScore.query.filter_by(user_id=user_id).count()
    }
```

---

**🎉 Success!** You now have a complete understanding of how US-05 and US-06 work together to create an intelligent resume matching system with NLP-powered keyword extraction and visual similarity scoring.
