/**
 * US-07: Suggestions Frontend JavaScript
 * =====================================
 * 
 * This file handles the frontend functionality for the suggestions feature
 * in the Dr. Resume application. It manages both basic and premium suggestions,
 * user interactions, and API communication.
 * 
 * Features:
 * - Load and display basic suggestions
 * - Generate and display premium AI suggestions
 * - Handle suggestion status updates (implement/dismiss)
 * - Manage premium access and upgrade prompts
 * - Real-time UI updates and animations
 * 
 * Author: Dr. Resume Development Team
 * Date: 2025-07-23
 * Version: 1.0.0
 */

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://localhost:5007/api',
    ENDPOINTS: {
        BASIC_SUGGESTIONS: '/suggestions',
        PREMIUM_SUGGESTIONS: '/premium-suggestions',
        SUGGESTION_STATUS: '/suggestions'
    },
    STORAGE_KEYS: {
        AUTH_TOKEN: 'dr_resume_token',
        USER_DATA: 'dr_resume_user',
        RESUME_ID: 'current_resume_id',
        JD_ID: 'current_jd_id'
    }
};

// Global state
let currentUser = null;
let currentResumeId = null;
let currentJdId = null;
let authToken = null;
let basicSuggestions = [];
let premiumSuggestions = null;

// DOM elements
let elements = {};

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeAuth();
    setupEventListeners();
    loadSuggestions();
});

/**
 * Initialize DOM element references
 */
function initializeElements() {
    elements = {
        loadingSpinner: document.getElementById('loadingSpinner'),
        basicSuggestions: document.getElementById('basicSuggestions'),
        premiumSuggestions: document.getElementById('premiumSuggestions'),
        premiumSection: document.getElementById('premiumSection'),
        premiumSuggestionsSection: document.getElementById('premiumSuggestionsSection'),
        emptyState: document.getElementById('emptyState'),
        generatePremiumBtn: document.getElementById('generatePremiumBtn'),
        refreshSuggestions: document.getElementById('refreshSuggestions'),
        userProfile: document.getElementById('userProfile'),
        logoutBtn: document.getElementById('logoutBtn')
    };
}

/**
 * Initialize authentication and user data
 */
function initializeAuth() {
    authToken = localStorage.getItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    const userData = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_DATA);
    currentResumeId = localStorage.getItem(CONFIG.STORAGE_KEYS.RESUME_ID) || 'sample-resume-456';
    currentJdId = localStorage.getItem(CONFIG.STORAGE_KEYS.JD_ID) || 'sample-jd-789';
    
    if (userData) {
        currentUser = JSON.parse(userData);
        updateUserInterface();
    }
    
    if (!authToken) {
        // For demo purposes, create a test token
        createTestToken();
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Premium suggestions button
    elements.generatePremiumBtn.addEventListener('click', generatePremiumSuggestions);
    
    // Refresh suggestions button
    elements.refreshSuggestions.addEventListener('click', function() {
        loadSuggestions(true);
    });
    
    // Logout button
    elements.logoutBtn.addEventListener('click', logout);
}

/**
 * Update user interface based on user data
 */
function updateUserInterface() {
    if (currentUser) {
        elements.userProfile.innerHTML = `<i class="fas fa-user me-1"></i>${currentUser.email || 'User'}`;
        
        // Hide premium section if user already has premium access
        if (currentUser.is_premium) {
            elements.premiumSection.style.display = 'none';
        }
    }
}

/**
 * Load suggestions from the API
 */
async function loadSuggestions(forceRefresh = false) {
    try {
        showLoading(true);
        hideEmptyState();
        
        // Load basic suggestions
        await loadBasicSuggestions(forceRefresh);
        
        // Load premium suggestions if user has access
        if (currentUser && currentUser.is_premium) {
            await loadPremiumSuggestions();
        }
        
    } catch (error) {
        console.error('Error loading suggestions:', error);
        showError('Failed to load suggestions. Please try again.');
    } finally {
        showLoading(false);
    }
}

/**
 * Load basic suggestions
 */
async function loadBasicSuggestions(forceRefresh = false) {
    try {
        const response = await fetch(
            `${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.BASIC_SUGGESTIONS}/${currentResumeId}/${currentJdId}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const data = await response.json();
        
        if (data.success) {
            basicSuggestions = data.data.suggestions;
            renderBasicSuggestions();
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error loading basic suggestions:', error);
        showError('Failed to load basic suggestions.');
    }
}

/**
 * Generate premium suggestions
 */
async function generatePremiumSuggestions() {
    try {
        showLoading(true);
        elements.generatePremiumBtn.disabled = true;
        elements.generatePremiumBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
        
        const response = await fetch(
            `${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.PREMIUM_SUGGESTIONS}`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    resume_id: currentResumeId,
                    job_description_id: currentJdId,
                    include_skill_analysis: true,
                    include_experience_optimization: true
                })
            }
        );
        
        const data = await response.json();
        
        if (data.success) {
            premiumSuggestions = data.data;
            renderPremiumSuggestions();
            elements.premiumSection.style.display = 'none';
            elements.premiumSuggestionsSection.style.display = 'block';
        } else if (data.upgrade_required) {
            showUpgradeModal();
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error generating premium suggestions:', error);
        showError('Failed to generate premium suggestions.');
    } finally {
        showLoading(false);
        elements.generatePremiumBtn.disabled = false;
        elements.generatePremiumBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Generate Premium Suggestions';
    }
}

/**
 * Render basic suggestions
 */
function renderBasicSuggestions() {
    if (!basicSuggestions || basicSuggestions.length === 0) {
        showEmptyState();
        return;
    }
    
    const suggestionsHtml = basicSuggestions.map(suggestion => {
        const priorityClass = `priority-${suggestion.priority}`;
        const priorityColor = getPriorityColor(suggestion.priority);
        const confidenceWidth = Math.round(suggestion.confidence_score * 100);
        
        return `
            <div class="suggestion-card ${priorityClass} card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title fw-bold mb-0">${suggestion.title}</h5>
                        <span class="badge priority-badge" style="background-color: ${priorityColor}">
                            ${suggestion.priority}
                        </span>
                    </div>
                    
                    <p class="card-text text-muted mb-3">${suggestion.description}</p>
                    
                    ${suggestion.suggested_keywords && suggestion.suggested_keywords.length > 0 ? `
                        <div class="mb-3">
                            <small class="text-muted fw-bold">Suggested Keywords:</small><br>
                            ${suggestion.suggested_keywords.map(keyword => 
                                `<span class="keyword-tag">${keyword}</span>`
                            ).join('')}
                        </div>
                    ` : ''}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <small class="text-muted">Confidence Score</small>
                            <div class="confidence-bar">
                                <div class="confidence-fill bg-success" style="width: ${confidenceWidth}%"></div>
                            </div>
                            <small class="text-muted">${confidenceWidth}%</small>
                        </div>
                        <div class="col-md-6">
                            <div class="impact-indicator">
                                <i class="fas fa-chart-line"></i>
                                <span>+${suggestion.expected_impact}% match score</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="suggestion-actions">
                        <button class="btn btn-action btn-implement" 
                                onclick="updateSuggestionStatus('${suggestion.id}', 'implemented')">
                            <i class="fas fa-check me-1"></i>Mark as Implemented
                        </button>
                        <button class="btn btn-action btn-dismiss" 
                                onclick="updateSuggestionStatus('${suggestion.id}', 'dismissed')">
                            <i class="fas fa-times me-1"></i>Dismiss
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    elements.basicSuggestions.innerHTML = suggestionsHtml;
}

/**
 * Render premium suggestions
 */
function renderPremiumSuggestions() {
    if (!premiumSuggestions) return;
    
    const recommendationsHtml = premiumSuggestions.detailed_recommendations.map(rec => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="fw-bold text-primary">${rec.category}</h6>
                    <span class="badge bg-${rec.priority.toLowerCase() === 'high' ? 'danger' : rec.priority.toLowerCase() === 'medium' ? 'warning' : 'info'}">
                        ${rec.priority}
                    </span>
                </div>
                <p class="mb-2">${rec.recommendation}</p>
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>Timeline: ${rec.timeline} | 
                    <i class="fas fa-chart-line me-1"></i>Impact: ${rec.expected_impact}
                </small>
            </div>
        </div>
    `).join('');
    
    const premiumHtml = `
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI Analysis
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">${premiumSuggestions.ai_analysis}</p>
            </div>
        </div>
        
        <div class="mt-4">
            <h6 class="fw-bold mb-3">Detailed Recommendations</h6>
            ${recommendationsHtml}
        </div>
        
        ${premiumSuggestions.personalized_tips && premiumSuggestions.personalized_tips.length > 0 ? `
            <div class="mt-4">
                <h6 class="fw-bold mb-3">Personalized Tips</h6>
                <ul class="list-group list-group-flush">
                    ${premiumSuggestions.personalized_tips.map(tip => 
                        `<li class="list-group-item"><i class="fas fa-lightbulb text-warning me-2"></i>${tip}</li>`
                    ).join('')}
                </ul>
            </div>
        ` : ''}
    `;
    
    elements.premiumSuggestions.innerHTML = premiumHtml;
}

/**
 * Update suggestion status
 */
async function updateSuggestionStatus(suggestionId, status) {
    try {
        const statusData = {};
        if (status === 'implemented') {
            statusData.is_implemented = true;
        } else if (status === 'dismissed') {
            statusData.is_dismissed = true;
        }
        
        const response = await fetch(
            `${CONFIG.API_BASE_URL}${CONFIG.ENDPOINTS.SUGGESTION_STATUS}/${suggestionId}/status`,
            {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(statusData)
            }
        );
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess(`Suggestion ${status} successfully`);
            loadBasicSuggestions(); // Refresh suggestions
        } else {
            throw new Error(data.message);
        }
        
    } catch (error) {
        console.error('Error updating suggestion status:', error);
        showError('Failed to update suggestion status.');
    }
}

/**
 * Utility functions
 */
function getPriorityColor(priority) {
    const colors = {
        'high': '#e74c3c',
        'medium': '#f39c12',
        'low': '#17a2b8',
        'critical': '#dc3545'
    };
    return colors[priority] || colors.medium;
}

function showLoading(show) {
    elements.loadingSpinner.style.display = show ? 'block' : 'none';
}

function showEmptyState() {
    elements.emptyState.style.display = 'block';
    elements.basicSuggestions.innerHTML = '';
}

function hideEmptyState() {
    elements.emptyState.style.display = 'none';
}

function showSuccess(message) {
    // Simple alert for now - could be replaced with toast notifications
    alert('Success: ' + message);
}

function showError(message) {
    // Simple alert for now - could be replaced with toast notifications
    alert('Error: ' + message);
}

function showUpgradeModal() {
    alert('Premium access required! Please upgrade your account to access AI-powered suggestions.');
}

function logout() {
    localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_DATA);
    window.location.href = '../US-01-User-Registration/frontend/us01_home.html';
}

/**
 * Create test token for development
 */
async function createTestToken() {
    try {
        const response = await fetch('http://localhost:5007/api/dev/create-test-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: 'test-user-123',
                is_premium: false // Set to true to test premium features
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, authToken);
            
            currentUser = {
                id: data.user_id,
                email: '<EMAIL>',
                is_premium: data.is_premium
            };
            localStorage.setItem(CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(currentUser));
            
            updateUserInterface();
        }
    } catch (error) {
        console.error('Error creating test token:', error);
    }
}

// Make functions available globally for onclick handlers
window.updateSuggestionStatus = updateSuggestionStatus;
