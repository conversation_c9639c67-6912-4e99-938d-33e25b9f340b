"""
US-09: API Protection Flask Application
=======================================

Main Flask application for the API protection feature in the Dr. Resume application.
This app provides comprehensive security, rate limiting, threat detection, and monitoring.

Features:
- Advanced threat detection and blocking
- Intelligent rate limiting with multiple strategies
- Input validation and sanitization
- Security event logging and monitoring
- API key management
- Comprehensive audit logging
- Real-time security analytics

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import logging
from datetime import datetime, timedelta

from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
from dotenv import load_dotenv

# Local imports
from us09_security_model import db, SecurityEvent, SecurityRule, APIKey, AuditLog
from us09_security_middleware import SecurityMiddleware
# from us09_security_routes import security_bp  # Will be imported after creation

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app(config_name='development'):
    """Create and configure the Flask application"""
    
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = get_database_url()
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = os.getenv('SQLALCHEMY_ECHO', 'False').lower() == 'true'
    
    # JWT Configuration
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', app.config['SECRET_KEY'])
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    app.config['JWT_ALGORITHM'] = 'HS256'
    
    # Security Configuration
    app.config['SECURITY_ENABLED'] = os.getenv('SECURITY_ENABLED', 'True').lower() == 'true'
    app.config['THREAT_DETECTION_ENABLED'] = os.getenv('THREAT_DETECTION_ENABLED', 'True').lower() == 'true'
    app.config['RATE_LIMITING_ENABLED'] = os.getenv('RATE_LIMITING_ENABLED', 'True').lower() == 'true'
    app.config['AUTO_BLOCKING_ENABLED'] = os.getenv('AUTO_BLOCKING_ENABLED', 'True').lower() == 'true'
    
    # Rate Limiting Configuration
    app.config['RATELIMIT_STORAGE_URL'] = os.getenv('REDIS_URL', 'memory://')
    app.config['RATELIMIT_DEFAULT'] = os.getenv('RATELIMIT_DEFAULT', '1000 per hour')
    
    # Security Headers Configuration
    app.config['FORCE_HTTPS'] = os.getenv('FORCE_HTTPS', 'False').lower() == 'true'
    app.config['SESSION_COOKIE_SECURE'] = app.config['FORCE_HTTPS']
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    
    # Audit Logging Configuration
    app.config['AUDIT_LOGGING_ENABLED'] = os.getenv('AUDIT_LOGGING_ENABLED', 'True').lower() == 'true'
    app.config['SECURITY_EVENT_RETENTION_DAYS'] = int(os.getenv('SECURITY_EVENT_RETENTION_DAYS', '90'))
    
    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)
    
    # CORS Configuration
    cors_origins = os.getenv('CORS_ORIGINS', '*').split(',')
    CORS(app, origins=cors_origins, supports_credentials=True)
    
    # Security Headers (Talisman)
    if app.config['FORCE_HTTPS']:
        Talisman(app, force_https=True)
    else:
        Talisman(app, force_https=False)
    
    # Initialize Security Middleware
    if app.config['SECURITY_ENABLED']:
        security_middleware = SecurityMiddleware(app)
        logger.info("Security middleware initialized")
    
    # Initialize Rate Limiter
    if app.config['RATE_LIMITING_ENABLED']:
        limiter = Limiter(
            app,
            key_func=get_remote_address,
            default_limits=[app.config['RATELIMIT_DEFAULT']]
        )
        logger.info("Rate limiter initialized")
    
    # Register blueprints
    from us09_security_routes import security_bp
    app.register_blueprint(security_bp)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }), 401
    
    # Security-specific error handlers
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'success': False,
            'message': 'Access forbidden - security violation detected',
            'error': 'security_violation'
        }), 403
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return jsonify({
            'success': False,
            'message': 'Rate limit exceeded - please slow down',
            'error': 'rate_limit_exceeded',
            'retry_after': str(e.retry_after) if hasattr(e, 'retry_after') else '60'
        }), 429
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'success': False,
            'message': 'Bad request - invalid or malicious input detected',
            'error': 'bad_request'
        }), 400
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        logger.error(f"Internal server error: {str(error)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'internal_error'
        }), 500
    
    # Health check routes
    @app.route('/health', methods=['GET'])
    def health_check():
        """Application health check"""
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'healthy'
        except Exception as e:
            db_status = f'unhealthy: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'US-09 API Protection service is running',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': db_status,
            'security_features': {
                'threat_detection': app.config['THREAT_DETECTION_ENABLED'],
                'rate_limiting': app.config['RATE_LIMITING_ENABLED'],
                'auto_blocking': app.config['AUTO_BLOCKING_ENABLED'],
                'audit_logging': app.config['AUDIT_LOGGING_ENABLED'],
                'https_enforced': app.config['FORCE_HTTPS']
            }
        }), 200
    
    @app.route('/api/health', methods=['GET'])
    def api_health_check():
        """API health check with security statistics"""
        try:
            # Get security statistics
            total_events = SecurityEvent.query.count()
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.created_at > datetime.utcnow() - timedelta(hours=24)
            ).count()
            
            active_api_keys = APIKey.query.filter_by(status='active').count()
            
            return jsonify({
                'success': True,
                'message': 'API is healthy and secure',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'US-09 API Protection',
                'version': '1.0.0',
                'security_statistics': {
                    'total_security_events': total_events,
                    'recent_security_events_24h': recent_events,
                    'active_api_keys': active_api_keys
                },
                'endpoints': {
                    'security_dashboard': '/api/security/dashboard',
                    'security_events': '/api/security/events',
                    'api_keys': '/api/security/api-keys',
                    'security_rules': '/api/security/rules'
                }
            }), 200
            
        except Exception as e:
            logger.error(f"API health check failed: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'API health check failed',
                'error': str(e)
            }), 500
    
    # Security monitoring endpoint
    @app.route('/api/security/status', methods=['GET'])
    @jwt_required()
    def security_status():
        """Get current security status"""
        try:
            # Calculate security metrics
            now = datetime.utcnow()
            last_hour = now - timedelta(hours=1)
            last_24h = now - timedelta(hours=24)
            
            # Recent security events
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.created_at > last_hour
            ).count()
            
            # High severity events in last 24h
            high_severity_events = SecurityEvent.query.filter(
                SecurityEvent.created_at > last_24h,
                SecurityEvent.severity.in_(['high', 'critical'])
            ).count()
            
            # Blocked requests in last hour
            blocked_requests = SecurityEvent.query.filter(
                SecurityEvent.created_at > last_hour,
                SecurityEvent.is_blocked == True
            ).count()
            
            return jsonify({
                'success': True,
                'message': 'Security status retrieved successfully',
                'data': {
                    'timestamp': now.isoformat(),
                    'security_level': 'normal',  # Could be calculated based on metrics
                    'metrics': {
                        'recent_events_1h': recent_events,
                        'high_severity_events_24h': high_severity_events,
                        'blocked_requests_1h': blocked_requests
                    },
                    'system_status': {
                        'threat_detection': 'active',
                        'rate_limiting': 'active',
                        'auto_blocking': 'active'
                    }
                }
            }), 200
            
        except Exception as e:
            logger.error(f"Error getting security status: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'Failed to retrieve security status',
                'error': str(e)
            }), 500
    
    # Development routes (only in development mode)
    if os.getenv('FLASK_ENV') == 'development':
        @app.route('/api/dev/create-test-token', methods=['POST'])
        def create_test_token():
            """Create a test JWT token for development"""
            data = request.get_json() or {}
            user_id = data.get('user_id', 'test-user-123')
            is_premium = data.get('is_premium', False)
            
            additional_claims = {
                'role': 'premium' if is_premium else 'basic',
                'is_premium': is_premium
            }
            
            token = create_access_token(
                identity=user_id,
                additional_claims=additional_claims
            )
            
            return jsonify({
                'success': True,
                'message': 'Test token created',
                'token': token,
                'user_id': user_id,
                'is_premium': is_premium
            }), 200
        
        @app.route('/api/dev/trigger-security-event', methods=['POST'])
        def trigger_security_event():
            """Trigger a test security event for development"""
            data = request.get_json() or {}
            event_type = data.get('event_type', 'suspicious_activity')
            
            # Create a test security event
            event = SecurityEvent(
                event_type=event_type,
                severity='medium',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', ''),
                request_method=request.method,
                request_path=request.path,
                event_description=f"Test security event: {event_type}",
                event_data={'test': True}
            )
            
            db.session.add(event)
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'Test security event created',
                'event_id': str(event.id)
            }), 200
    
    # Database initialization
    @app.before_first_request
    def create_tables():
        """Create database tables if they don't exist"""
        try:
            db.create_all()
            logger.info("Database tables created successfully")
            
            # Create default security rules
            create_default_security_rules()
            
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
    
    return app


def get_database_url():
    """Construct database URL from environment variables"""
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'dr_resume_db')
    db_user = os.getenv('DB_USER', 'dr_resume_user')
    db_password = os.getenv('DB_PASSWORD', 'password')
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"


def create_default_security_rules():
    """Create default security rules"""
    default_rules = [
        {
            'rule_name': 'global_rate_limit',
            'rule_description': 'Global rate limit for all endpoints',
            'rule_category': 'rate_limit',
            'max_requests': 1000,
            'time_window_seconds': 3600,
            'is_enabled': True,
            'priority': 100
        },
        {
            'rule_name': 'auth_rate_limit',
            'rule_description': 'Rate limit for authentication endpoints',
            'rule_category': 'rate_limit',
            'max_requests': 10,
            'time_window_seconds': 300,
            'is_enabled': True,
            'priority': 200
        },
        {
            'rule_name': 'block_sql_injection',
            'rule_description': 'Block requests with SQL injection patterns',
            'rule_category': 'threat_detection',
            'rule_config': {'auto_block': True, 'threshold': 0.8},
            'is_enabled': True,
            'priority': 300
        }
    ]
    
    for rule_data in default_rules:
        existing = SecurityRule.query.filter_by(rule_name=rule_data['rule_name']).first()
        if not existing:
            rule = SecurityRule(**rule_data)
            db.session.add(rule)
    
    try:
        db.session.commit()
        logger.info("Default security rules created")
    except Exception as e:
        logger.error(f"Error creating default security rules: {str(e)}")
        db.session.rollback()


# Create the Flask application
app = create_app()

if __name__ == '__main__':
    # Development server
    debug_mode = os.getenv('FLASK_ENV') == 'development'
    port = int(os.getenv('PORT', 5009))  # US-09 uses port 5009
    
    logger.info(f"Starting US-09 API Protection service on port {port}")
    logger.info(f"Debug mode: {debug_mode}")
    logger.info(f"Security features enabled: {app.config['SECURITY_ENABLED']}")
    logger.info(f"Threat detection enabled: {app.config['THREAT_DETECTION_ENABLED']}")
    logger.info(f"Rate limiting enabled: {app.config['RATE_LIMITING_ENABLED']}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode,
        threaded=True
    )
