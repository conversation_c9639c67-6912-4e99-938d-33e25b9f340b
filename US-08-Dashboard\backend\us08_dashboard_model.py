"""
US-08: Dashboard Model
======================

This module defines the database models for the dashboard feature in the Dr. Resume application.
It includes models for tracking scan history, analytics, and user activity.

Models:
- ScanHistory: Comprehensive scan history tracking
- DashboardAnalytics: Aggregated analytics data
- UserActivity: User activity tracking
- ExportHistory: Export and report generation history

Author: Dr. <PERSON>sume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import uuid
from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy import func, text
from enum import Enum

# Initialize SQLAlchemy (will be configured in main app)
db = SQLAlchemy()


class ScanStatus(Enum):
    """Enumeration for scan status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExportFormat(Enum):
    """Enumeration for export formats"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"


class ScanHistory(db.Model):
    """
    Model for tracking comprehensive scan history
    
    This model aggregates data from all previous US features to provide
    a complete view of user's resume scanning activities.
    """
    __tablename__ = 'scan_history'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User information
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Related entities (from previous US)
    resume_id = db.Column(UUID(as_uuid=True), nullable=False)
    job_description_id = db.Column(UUID(as_uuid=True), nullable=False)
    matching_score_id = db.Column(UUID(as_uuid=True), nullable=True)
    
    # Scan metadata
    scan_name = db.Column(db.String(200), nullable=False)
    scan_description = db.Column(db.Text)
    scan_status = db.Column(db.Enum(ScanStatus), default=ScanStatus.PENDING)
    
    # Resume information
    resume_filename = db.Column(db.String(255))
    resume_file_size = db.Column(db.Integer)  # in bytes
    resume_upload_date = db.Column(db.DateTime)
    
    # Job description information
    job_title = db.Column(db.String(200))
    company_name = db.Column(db.String(200))
    job_location = db.Column(db.String(200))
    job_type = db.Column(db.String(50))  # full-time, part-time, contract, etc.
    
    # Matching results
    overall_match_score = db.Column(db.Float, default=0.0)
    keyword_match_score = db.Column(db.Float, default=0.0)
    skill_match_score = db.Column(db.Float, default=0.0)
    experience_match_score = db.Column(db.Float, default=0.0)
    
    # Keywords analysis
    total_keywords_found = db.Column(db.Integer, default=0)
    missing_keywords_count = db.Column(db.Integer, default=0)
    matched_keywords = db.Column(JSON, default=list)
    missing_keywords = db.Column(JSON, default=list)
    
    # Suggestions summary
    total_suggestions = db.Column(db.Integer, default=0)
    high_priority_suggestions = db.Column(db.Integer, default=0)
    implemented_suggestions = db.Column(db.Integer, default=0)
    dismissed_suggestions = db.Column(db.Integer, default=0)
    
    # Premium features usage
    has_premium_suggestions = db.Column(db.Boolean, default=False)
    premium_cost = db.Column(db.Float, default=0.0)
    ai_tokens_used = db.Column(db.Integer, default=0)
    
    # Processing metrics
    processing_time_seconds = db.Column(db.Float, default=0.0)
    processing_start_time = db.Column(db.DateTime)
    processing_end_time = db.Column(db.DateTime)
    
    # User interaction tracking
    last_viewed_at = db.Column(db.DateTime)
    view_count = db.Column(db.Integer, default=0)
    is_bookmarked = db.Column(db.Boolean, default=False)
    user_rating = db.Column(db.Integer)  # 1-5 stars
    user_notes = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<ScanHistory {self.id}: {self.scan_name}>'
    
    def to_dict(self):
        """Convert scan history to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'resume_id': str(self.resume_id),
            'job_description_id': str(self.job_description_id),
            'scan_name': self.scan_name,
            'scan_description': self.scan_description,
            'scan_status': self.scan_status.value if self.scan_status else None,
            'resume_filename': self.resume_filename,
            'job_title': self.job_title,
            'company_name': self.company_name,
            'job_location': self.job_location,
            'job_type': self.job_type,
            'overall_match_score': self.overall_match_score,
            'keyword_match_score': self.keyword_match_score,
            'skill_match_score': self.skill_match_score,
            'experience_match_score': self.experience_match_score,
            'total_keywords_found': self.total_keywords_found,
            'missing_keywords_count': self.missing_keywords_count,
            'matched_keywords': self.matched_keywords,
            'missing_keywords': self.missing_keywords,
            'total_suggestions': self.total_suggestions,
            'high_priority_suggestions': self.high_priority_suggestions,
            'implemented_suggestions': self.implemented_suggestions,
            'dismissed_suggestions': self.dismissed_suggestions,
            'has_premium_suggestions': self.has_premium_suggestions,
            'premium_cost': self.premium_cost,
            'processing_time_seconds': self.processing_time_seconds,
            'last_viewed_at': self.last_viewed_at.isoformat() if self.last_viewed_at else None,
            'view_count': self.view_count,
            'is_bookmarked': self.is_bookmarked,
            'user_rating': self.user_rating,
            'user_notes': self.user_notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_improvement_percentage(self):
        """Calculate improvement percentage from suggestions"""
        if self.total_suggestions == 0:
            return 0.0
        return (self.implemented_suggestions / self.total_suggestions) * 100
    
    def get_priority_distribution(self):
        """Get distribution of suggestion priorities"""
        return {
            'high': self.high_priority_suggestions,
            'medium': max(0, self.total_suggestions - self.high_priority_suggestions - 
                         (self.total_suggestions - self.high_priority_suggestions) // 3),
            'low': (self.total_suggestions - self.high_priority_suggestions) // 3
        }


class DashboardAnalytics(db.Model):
    """
    Model for storing aggregated analytics data for dashboard
    
    This model stores pre-calculated analytics to improve dashboard performance
    """
    __tablename__ = 'dashboard_analytics'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User information
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Time period for analytics
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    period_type = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly, yearly
    
    # Scan statistics
    total_scans = db.Column(db.Integer, default=0)
    completed_scans = db.Column(db.Integer, default=0)
    failed_scans = db.Column(db.Integer, default=0)
    
    # Score statistics
    average_match_score = db.Column(db.Float, default=0.0)
    highest_match_score = db.Column(db.Float, default=0.0)
    lowest_match_score = db.Column(db.Float, default=0.0)
    score_improvement = db.Column(db.Float, default=0.0)  # compared to previous period
    
    # Keyword statistics
    total_keywords_analyzed = db.Column(db.Integer, default=0)
    average_missing_keywords = db.Column(db.Float, default=0.0)
    most_common_missing_keywords = db.Column(JSON, default=list)
    
    # Suggestion statistics
    total_suggestions_generated = db.Column(db.Integer, default=0)
    suggestions_implemented = db.Column(db.Integer, default=0)
    suggestions_dismissed = db.Column(db.Integer, default=0)
    implementation_rate = db.Column(db.Float, default=0.0)
    
    # Premium usage statistics
    premium_scans = db.Column(db.Integer, default=0)
    total_premium_cost = db.Column(db.Float, default=0.0)
    ai_tokens_consumed = db.Column(db.Integer, default=0)
    
    # Industry and job type analysis
    top_job_titles = db.Column(JSON, default=list)
    top_companies = db.Column(JSON, default=list)
    job_type_distribution = db.Column(JSON, default=dict)
    
    # Performance metrics
    average_processing_time = db.Column(db.Float, default=0.0)
    total_processing_time = db.Column(db.Float, default=0.0)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<DashboardAnalytics {self.user_id}: {self.period_type}>'
    
    def to_dict(self):
        """Convert analytics to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'period_type': self.period_type,
            'total_scans': self.total_scans,
            'completed_scans': self.completed_scans,
            'failed_scans': self.failed_scans,
            'average_match_score': self.average_match_score,
            'highest_match_score': self.highest_match_score,
            'lowest_match_score': self.lowest_match_score,
            'score_improvement': self.score_improvement,
            'total_keywords_analyzed': self.total_keywords_analyzed,
            'average_missing_keywords': self.average_missing_keywords,
            'most_common_missing_keywords': self.most_common_missing_keywords,
            'total_suggestions_generated': self.total_suggestions_generated,
            'suggestions_implemented': self.suggestions_implemented,
            'suggestions_dismissed': self.suggestions_dismissed,
            'implementation_rate': self.implementation_rate,
            'premium_scans': self.premium_scans,
            'total_premium_cost': self.total_premium_cost,
            'ai_tokens_consumed': self.ai_tokens_consumed,
            'top_job_titles': self.top_job_titles,
            'top_companies': self.top_companies,
            'job_type_distribution': self.job_type_distribution,
            'average_processing_time': self.average_processing_time,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class UserActivity(db.Model):
    """
    Model for tracking user activity and engagement
    """
    __tablename__ = 'user_activity'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User information
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Activity details
    activity_type = db.Column(db.String(50), nullable=False)  # login, scan, view, export, etc.
    activity_description = db.Column(db.String(200))
    
    # Related entities
    related_scan_id = db.Column(UUID(as_uuid=True), nullable=True)
    related_entity_type = db.Column(db.String(50))  # resume, job_description, suggestion, etc.
    related_entity_id = db.Column(UUID(as_uuid=True), nullable=True)
    
    # Activity metadata
    ip_address = db.Column(db.String(45))  # IPv6 compatible
    user_agent = db.Column(db.String(500))
    session_id = db.Column(db.String(100))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        return f'<UserActivity {self.user_id}: {self.activity_type}>'


class ExportHistory(db.Model):
    """
    Model for tracking export and report generation history
    """
    __tablename__ = 'export_history'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User information
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Export details
    export_type = db.Column(db.String(50), nullable=False)  # scan_report, analytics, history
    export_format = db.Column(db.Enum(ExportFormat), nullable=False)
    export_filename = db.Column(db.String(255), nullable=False)
    
    # Export parameters
    date_range_start = db.Column(db.DateTime)
    date_range_end = db.Column(db.DateTime)
    filters_applied = db.Column(JSON, default=dict)
    
    # Export metadata
    file_size_bytes = db.Column(db.Integer)
    generation_time_seconds = db.Column(db.Float)
    download_count = db.Column(db.Integer, default=0)
    
    # File storage
    file_path = db.Column(db.String(500))  # Local file path or cloud storage URL
    is_available = db.Column(db.Boolean, default=True)
    expires_at = db.Column(db.DateTime)  # Auto-cleanup date
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_downloaded_at = db.Column(db.DateTime)
    
    def __repr__(self):
        return f'<ExportHistory {self.user_id}: {self.export_filename}>'
    
    def to_dict(self):
        """Convert export history to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'export_type': self.export_type,
            'export_format': self.export_format.value if self.export_format else None,
            'export_filename': self.export_filename,
            'date_range_start': self.date_range_start.isoformat() if self.date_range_start else None,
            'date_range_end': self.date_range_end.isoformat() if self.date_range_end else None,
            'filters_applied': self.filters_applied,
            'file_size_bytes': self.file_size_bytes,
            'generation_time_seconds': self.generation_time_seconds,
            'download_count': self.download_count,
            'is_available': self.is_available,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_downloaded_at': self.last_downloaded_at.isoformat() if self.last_downloaded_at else None
        }
