<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Suggestions - Dr. Resume</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .main-container {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .suggestion-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .suggestion-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .priority-high {
            border-left: 5px solid var(--danger-color);
        }

        .priority-medium {
            border-left: 5px solid var(--warning-color);
        }

        .priority-low {
            border-left: 5px solid var(--info-color);
        }

        .priority-badge {
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .keyword-tag {
            background: linear-gradient(45deg, var(--secondary-color), #5dade2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            margin: 0.2rem;
            display: inline-block;
            font-weight: 500;
        }

        .premium-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .premium-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-100px) translateY(-100px); }
        }

        .premium-content {
            position: relative;
            z-index: 1;
        }

        .btn-premium {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            border: none;
            font-weight: bold;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .btn-premium:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
            color: #333;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .confidence-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            background-color: #e9ecef;
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.5s ease;
        }

        .impact-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--success-color);
            font-weight: 500;
        }

        .suggestion-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn-action {
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            border: none;
            transition: all 0.3s ease;
        }

        .btn-implement {
            background-color: var(--success-color);
            color: white;
        }

        .btn-dismiss {
            background-color: #6c757d;
            color: white;
        }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .main-container {
                margin-top: 1rem;
            }
            
            .suggestion-card {
                margin-bottom: 1rem;
            }
            
            .premium-section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-md me-2"></i>
                Dr. Resume
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../US-01-User-Registration/frontend/us01_home.html">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../US-03-Resume-Upload/frontend/us03_upload.html">
                            <i class="fas fa-upload me-1"></i>Upload Resume
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../US-06-Matching-Score/frontend/us06_matching.html">
                            <i class="fas fa-chart-line me-1"></i>Matching Score
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-lightbulb me-1"></i>Suggestions
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="userProfile">
                            <i class="fas fa-user me-1"></i>Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-container">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="display-6 fw-bold text-dark">
                            <i class="fas fa-lightbulb text-warning me-3"></i>
                            Resume Suggestions
                        </h1>
                        <p class="lead text-muted">Improve your resume with AI-powered recommendations</p>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary" id="refreshSuggestions">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Premium Section -->
        <div class="premium-section" id="premiumSection">
            <div class="premium-content">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h3 class="fw-bold mb-3">
                            <i class="fas fa-crown me-2"></i>
                            Unlock Premium AI Suggestions
                        </h3>
                        <p class="mb-3">
                            Get personalized, detailed recommendations powered by advanced AI. 
                            Receive industry insights, step-by-step improvement guides, and market-relevant tips.
                        </p>
                        <ul class="list-unstyled mb-0">
                            <li><i class="fas fa-check me-2"></i>AI-powered analysis</li>
                            <li><i class="fas fa-check me-2"></i>Industry-specific insights</li>
                            <li><i class="fas fa-check me-2"></i>Detailed implementation guides</li>
                            <li><i class="fas fa-check me-2"></i>Market trend analysis</li>
                        </ul>
                    </div>
                    <div class="col-md-4 text-center">
                        <button class="btn btn-premium btn-lg" id="generatePremiumBtn">
                            <i class="fas fa-magic me-2"></i>
                            Generate Premium Suggestions
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 text-muted">Analyzing your resume and generating suggestions...</p>
        </div>

        <!-- Basic Suggestions Section -->
        <div class="row">
            <div class="col-12">
                <h3 class="fw-bold mb-4">
                    <i class="fas fa-list-ul me-2 text-primary"></i>
                    Basic Suggestions
                </h3>
                <div id="basicSuggestions">
                    <!-- Basic suggestions will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Premium Suggestions Section -->
        <div class="row" id="premiumSuggestionsSection" style="display: none;">
            <div class="col-12">
                <h3 class="fw-bold mb-4">
                    <i class="fas fa-crown me-2 text-warning"></i>
                    Premium AI Suggestions
                </h3>
                <div id="premiumSuggestions">
                    <!-- Premium suggestions will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-search"></i>
            <h4>No Suggestions Available</h4>
            <p>Upload a resume and job description to get personalized suggestions.</p>
            <a href="../US-03-Resume-Upload/frontend/us03_upload.html" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>Upload Resume
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="us07_suggestions.js"></script>
</body>
</html>
