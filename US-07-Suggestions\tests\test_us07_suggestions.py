"""
US-07: Suggestions Test Suite
=============================

Comprehensive test suite for the suggestions feature in the Dr. Resume application.
Tests both basic keyword suggestions and premium AI-powered suggestions.

Test Categories:
- Unit tests for suggestion generation logic
- API endpoint tests
- Database model tests
- OpenAI service integration tests
- Authentication and authorization tests

Author: Dr. <PERSON>sume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import pytest
import json
import uuid
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

# Flask and testing imports
from flask import Flask
from flask_testing import TestCase

# Local imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from us07_app import create_app
from us07_suggestions_model import db, Suggestion, PremiumSuggestion, SuggestionType, SuggestionPriority
from us07_suggestions_generator import SuggestionsGenerator, KeywordAnalyzer
from us07_openai_service import OpenAIService, PromptTemplates


class SuggestionsTestCase(TestCase):
    """Base test case for suggestions tests"""
    
    def create_app(self):
        """Create test Flask application"""
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['WTF_CSRF_ENABLED'] = False
        app.config['OPENAI_API_KEY'] = 'test-api-key'
        return app
    
    def setUp(self):
        """Set up test database and sample data"""
        db.create_all()
        self.create_sample_data()
    
    def tearDown(self):
        """Clean up test database"""
        db.session.remove()
        db.drop_all()
    
    def create_sample_data(self):
        """Create sample data for testing"""
        self.sample_user_id = str(uuid.uuid4())
        self.sample_resume_id = str(uuid.uuid4())
        self.sample_jd_id = str(uuid.uuid4())
        
        # Sample suggestion
        self.sample_suggestion = Suggestion(
            user_id=self.sample_user_id,
            resume_id=self.sample_resume_id,
            job_description_id=self.sample_jd_id,
            suggestion_type=SuggestionType.MISSING_KEYWORD,
            priority=SuggestionPriority.HIGH,
            title="Add missing keyword: Python",
            description="Python appears in job description but missing from resume",
            missing_keywords=["Python", "Django"],
            suggested_keywords=["Python", "Django", "Flask"],
            confidence_score=0.9,
            implementation_difficulty=2,
            expected_impact=15.0
        )
        
        db.session.add(self.sample_suggestion)
        db.session.commit()
    
    def get_auth_headers(self, is_premium=False):
        """Get authentication headers for testing"""
        # Mock JWT token for testing
        return {
            'Authorization': 'Bearer test-token',
            'Content-Type': 'application/json'
        }


class TestSuggestionsGenerator(SuggestionsTestCase):
    """Test the suggestions generator logic"""
    
    def setUp(self):
        super().setUp()
        self.generator = SuggestionsGenerator()
    
    def test_keyword_analyzer_initialization(self):
        """Test keyword analyzer initialization"""
        analyzer = KeywordAnalyzer()
        self.assertIsNotNone(analyzer)
    
    def test_extract_keywords(self):
        """Test keyword extraction from text"""
        analyzer = KeywordAnalyzer()
        text = "Python developer with experience in Django, Flask, and PostgreSQL"
        keywords = analyzer.extract_keywords(text)
        
        self.assertIsInstance(keywords, set)
        self.assertTrue(len(keywords) > 0)
    
    def test_calculate_keyword_similarity(self):
        """Test keyword similarity calculation"""
        analyzer = KeywordAnalyzer()
        keywords1 = {'python', 'django', 'flask'}
        keywords2 = {'python', 'react', 'javascript'}
        
        similarity = analyzer.calculate_keyword_similarity(keywords1, keywords2)
        self.assertIsInstance(similarity, float)
        self.assertGreaterEqual(similarity, 0.0)
        self.assertLessEqual(similarity, 1.0)
    
    def test_generate_suggestions(self):
        """Test suggestion generation"""
        resume_keywords = {'python', 'flask', 'sql'}
        jd_keywords = {'python', 'django', 'postgresql', 'react'}
        
        suggestions = self.generator.generate_suggestions(
            resume_keywords, jd_keywords, 
            "Python developer resume", "Django developer job"
        )
        
        self.assertIsInstance(suggestions, list)
        self.assertTrue(len(suggestions) > 0)
        
        # Check suggestion structure
        for suggestion in suggestions:
            self.assertIn('type', suggestion)
            self.assertIn('title', suggestion)
            self.assertIn('description', suggestion)
            self.assertIn('confidence_score', suggestion)
    
    def test_missing_keyword_suggestions(self):
        """Test missing keyword suggestion generation"""
        resume_keywords = {'python', 'flask'}
        jd_keywords = {'python', 'django', 'postgresql'}
        
        suggestions = self.generator.generate_suggestions(resume_keywords, jd_keywords)
        
        # Should have suggestions for missing keywords
        missing_keyword_suggestions = [
            s for s in suggestions 
            if s['type'] == SuggestionType.MISSING_KEYWORD.value
        ]
        
        self.assertTrue(len(missing_keyword_suggestions) > 0)


class TestSuggestionsAPI(SuggestionsTestCase):
    """Test the suggestions API endpoints"""
    
    @patch('us07_suggestions_routes.get_user_keywords_and_text')
    @patch('us07_suggestions_routes.get_jwt_identity')
    def test_get_basic_suggestions(self, mock_jwt_identity, mock_get_keywords):
        """Test getting basic suggestions"""
        mock_jwt_identity.return_value = self.sample_user_id
        mock_get_keywords.return_value = (
            {'python', 'flask'}, 
            {'python', 'django'}, 
            "Resume text", 
            "Job description text"
        )
        
        response = self.client.get(
            f'/api/suggestions/{self.sample_resume_id}/{self.sample_jd_id}',
            headers=self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('suggestions', data['data'])
    
    def test_get_basic_suggestions_invalid_ids(self):
        """Test getting suggestions with invalid IDs"""
        with patch('us07_suggestions_routes.get_jwt_identity') as mock_jwt:
            mock_jwt.return_value = self.sample_user_id
            
            response = self.client.get(
                '/api/suggestions/invalid/invalid',
                headers=self.get_auth_headers()
            )
            
            # Should handle gracefully
            self.assertIn(response.status_code, [400, 500])
    
    @patch('us07_suggestions_routes.get_jwt_identity')
    @patch('us07_suggestions_routes.get_jwt')
    @patch('us07_suggestions_routes.get_user_keywords_and_text')
    @patch('us07_openai_service.OpenAIService.generate_premium_suggestions')
    def test_generate_premium_suggestions(self, mock_openai, mock_get_keywords, mock_get_jwt, mock_jwt_identity):
        """Test generating premium suggestions"""
        mock_jwt_identity.return_value = self.sample_user_id
        mock_get_jwt.return_value = {'role': 'premium', 'is_premium': True}
        mock_get_keywords.return_value = (
            {'python', 'flask'}, 
            {'python', 'django'}, 
            "Resume text", 
            "Job description text"
        )
        
        # Mock OpenAI response
        mock_openai.return_value = {
            'overall_assessment': 'Good resume with room for improvement',
            'improvement_areas': [],
            'personalized_tips': [],
            'industry_insights': {},
            'metadata': {
                'model_used': 'gpt-3.5-turbo',
                'tokens_used': 500,
                'cost_estimate': 0.001
            }
        }
        
        response = self.client.post(
            '/api/premium-suggestions',
            headers=self.get_auth_headers(is_premium=True),
            data=json.dumps({
                'resume_id': self.sample_resume_id,
                'job_description_id': self.sample_jd_id
            })
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    @patch('us07_suggestions_routes.get_jwt_identity')
    @patch('us07_suggestions_routes.get_jwt')
    def test_premium_suggestions_no_access(self, mock_get_jwt, mock_jwt_identity):
        """Test premium suggestions without premium access"""
        mock_jwt_identity.return_value = self.sample_user_id
        mock_get_jwt.return_value = {'role': 'basic', 'is_premium': False}
        
        response = self.client.post(
            '/api/premium-suggestions',
            headers=self.get_auth_headers(),
            data=json.dumps({
                'resume_id': self.sample_resume_id,
                'job_description_id': self.sample_jd_id
            })
        )
        
        self.assertEqual(response.status_code, 403)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertTrue(data.get('upgrade_required', False))
    
    @patch('us07_suggestions_routes.get_jwt_identity')
    def test_update_suggestion_status(self, mock_jwt_identity):
        """Test updating suggestion status"""
        mock_jwt_identity.return_value = self.sample_user_id
        
        response = self.client.put(
            f'/api/suggestions/{self.sample_suggestion.id}/status',
            headers=self.get_auth_headers(),
            data=json.dumps({
                'is_implemented': True
            })
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # Verify database update
        updated_suggestion = Suggestion.query.get(self.sample_suggestion.id)
        self.assertTrue(updated_suggestion.is_implemented)


class TestOpenAIService(SuggestionsTestCase):
    """Test the OpenAI service integration"""
    
    def setUp(self):
        super().setUp()
        self.openai_service = OpenAIService()
    
    def test_openai_service_initialization(self):
        """Test OpenAI service initialization"""
        self.assertIsNotNone(self.openai_service)
        self.assertEqual(self.openai_service.api_key, 'test-api-key')
    
    @patch('openai.OpenAI')
    def test_validate_api_key(self, mock_openai_client):
        """Test API key validation"""
        mock_client = Mock()
        mock_client.models.list.return_value = []
        mock_openai_client.return_value = mock_client
        
        service = OpenAIService()
        is_valid = service.validate_api_key()
        self.assertTrue(is_valid)
    
    def test_prompt_templates(self):
        """Test prompt templates"""
        self.assertIsNotNone(PromptTemplates.RESUME_ANALYSIS_PROMPT)
        self.assertIsNotNone(PromptTemplates.SKILL_ENHANCEMENT_PROMPT)
        self.assertIsNotNone(PromptTemplates.EXPERIENCE_OPTIMIZATION_PROMPT)
        
        # Test template formatting
        formatted = PromptTemplates.RESUME_ANALYSIS_PROMPT.format(
            resume_text="Test resume",
            job_description="Test job description",
            matching_score=75.5
        )
        self.assertIn("Test resume", formatted)
        self.assertIn("75.5", formatted)


class TestSuggestionModels(SuggestionsTestCase):
    """Test the suggestion database models"""
    
    def test_suggestion_model_creation(self):
        """Test creating a suggestion model"""
        suggestion = Suggestion(
            user_id=str(uuid.uuid4()),
            resume_id=str(uuid.uuid4()),
            job_description_id=str(uuid.uuid4()),
            suggestion_type=SuggestionType.SKILL_ENHANCEMENT,
            priority=SuggestionPriority.MEDIUM,
            title="Test suggestion",
            description="Test description"
        )
        
        db.session.add(suggestion)
        db.session.commit()
        
        self.assertIsNotNone(suggestion.id)
        self.assertEqual(suggestion.title, "Test suggestion")
    
    def test_suggestion_to_dict(self):
        """Test suggestion serialization"""
        suggestion_dict = self.sample_suggestion.to_dict()
        
        self.assertIsInstance(suggestion_dict, dict)
        self.assertIn('id', suggestion_dict)
        self.assertIn('title', suggestion_dict)
        self.assertIn('description', suggestion_dict)
        self.assertIn('suggestion_type', suggestion_dict)
    
    def test_premium_suggestion_model(self):
        """Test premium suggestion model"""
        premium_suggestion = PremiumSuggestion(
            user_id=self.sample_user_id,
            resume_id=self.sample_resume_id,
            job_description_id=self.sample_jd_id,
            ai_analysis="Test AI analysis",
            detailed_recommendations=[],
            personalized_tips=[],
            industry_insights={}
        )
        
        db.session.add(premium_suggestion)
        db.session.commit()
        
        self.assertIsNotNone(premium_suggestion.id)
        self.assertEqual(premium_suggestion.ai_analysis, "Test AI analysis")


class TestHealthEndpoints(SuggestionsTestCase):
    """Test health check endpoints"""
    
    def test_health_endpoint(self):
        """Test main health endpoint"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('version', data)
    
    def test_api_health_endpoint(self):
        """Test API health endpoint"""
        response = self.client.get('/api/health')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('statistics', data)


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
