# US-10: Account Settings - Complete Guide

## 📋 Overview

**US-10: Account Settings** is the comprehensive user account management center of the Dr. Resume - AI Resume Scanner application. This final user story implements complete user profile management, subscription handling, security settings, and account preferences.

### 🎯 What This US Accomplishes

- ✅ **Profile Management**: Complete user profile editing and customization
- ✅ **Security Settings**: Password changes, 2FA, and security preferences
- ✅ **Subscription Management**: Premium subscription handling and billing
- ✅ **Privacy Controls**: Data privacy settings and GDPR compliance
- ✅ **Notification Preferences**: Email and system notification settings
- ✅ **Account Data**: Export, backup, and account deletion options
- ✅ **Activity Monitoring**: Account activity logs and security monitoring
- ✅ **Multi-language Support**: Internationalization and localization

## 🏗️ Architecture Overview

```
US-10-Account-Settings/
├── backend/                           # Flask API server
│   ├── us10_account_model.py         # User account and settings models
│   ├── us10_profile_service.py       # Profile management service
│   ├── us10_subscription_service.py  # Subscription and billing service
│   ├── us10_security_service.py      # Account security service
│   ├── us10_account_routes.py        # API routes and endpoints
│   ├── us10_app.py                   # Main Flask application
│   └── requirements.txt              # Python dependencies
├── frontend/                         # HTML/CSS/JS client
│   ├── us10_account.html             # Main account settings interface
│   └── us10_account.js               # Frontend logic and interactions
├── database/                         # Database setup
│   ├── us10_schema.sql              # PostgreSQL schema for account data
│   └── us10_init_db.py              # Database initialization script
├── tests/                           # Test suite
│   └── test_us10_account.py         # Comprehensive test coverage
└── docs/                           # Documentation
    └── README_US10.md              # This file
```

## 🚀 Quick Start Guide

### Prerequisites

1. **Python 3.9+** installed
2. **PostgreSQL 13+** running
3. **Previous US completed** (US-01 through US-09)
4. **Email service** configured (SendGrid, SMTP, etc.)
5. **Payment processor** setup (Stripe for subscriptions)

### Step 1: Database Setup

1. **Ensure PostgreSQL is running** (from previous US setup)

2. **Run Database Schema**:
   ```bash
   cd US-10-Account-Settings/database
   psql -U dr_resume_user -d dr_resume_db -f us10_schema.sql
   ```

3. **Initialize Database**:
   ```bash
   python us10_init_db.py --sample-data
   ```

### Step 2: Backend Setup

1. **Create Virtual Environment**:
   ```bash
   cd US-10-Account-Settings/backend
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment Variables**:
   Create `.env` file in backend directory:
   ```env
   # Database Configuration (from previous US)
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=dr_resume_db
   DB_USER=dr_resume_user
   DB_PASSWORD=your_secure_password
   
   # Flask Configuration
   SECRET_KEY=your-super-secret-key-change-in-production
   JWT_SECRET_KEY=your-jwt-secret-key
   FLASK_ENV=development
   
   # Email Configuration
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USE_TLS=True
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-app-password
   SENDGRID_API_KEY=your-sendgrid-api-key
   
   # Payment Configuration
   STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   
   # File Upload Configuration
   UPLOAD_FOLDER=uploads
   MAX_CONTENT_LENGTH=********  # 16MB
   ALLOWED_EXTENSIONS=jpg,jpeg,png,gif
   
   # Security Configuration
   BCRYPT_LOG_ROUNDS=12
   PASSWORD_MIN_LENGTH=8
   REQUIRE_2FA=False
   
   # CORS Configuration
   CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
   ```

4. **Start Backend Server**:
   ```bash
   python us10_app.py
   ```

   Server will start at: `http://localhost:5010`

### Step 3: Frontend Setup

1. **Open Frontend**:
   ```bash
   cd US-10-Account-Settings/frontend
   
   # Using Python's built-in server
   python -m http.server 3000
   
   # Then open: http://localhost:3000/us10_account.html
   ```

## 🧪 Testing

### Run Backend Tests

```bash
cd US-10-Account-Settings/tests
pytest test_us10_account.py -v
```

### Manual Testing Checklist

1. **Profile Management**:
   - [ ] Open `http://localhost:3000/us10_account.html`
   - [ ] Update profile information
   - [ ] Upload profile picture
   - [ ] Change email address (with verification)
   - [ ] Update personal preferences

2. **Security Settings**:
   - [ ] Change password
   - [ ] Enable/disable 2FA
   - [ ] View login history
   - [ ] Manage active sessions

3. **Subscription Management**:
   - [ ] View current subscription
   - [ ] Upgrade to premium
   - [ ] Update payment method
   - [ ] View billing history

4. **Privacy and Data**:
   - [ ] Export account data
   - [ ] Update privacy settings
   - [ ] Request account deletion

## 📚 Key Features Explained

### 1. **Complete Profile Management**
- Personal information editing
- Profile picture upload and management
- Contact information updates
- Professional details and preferences

### 2. **Advanced Security Settings**
- Password strength validation and updates
- Two-factor authentication setup
- Login history and session management
- Security alerts and notifications

### 3. **Subscription and Billing**
- Premium subscription management
- Payment method updates
- Billing history and invoices
- Subscription cancellation and refunds

### 4. **Privacy and Data Control**
- GDPR-compliant data export
- Privacy settings management
- Account deletion with data purging
- Data retention policy controls

### 5. **Notification Preferences**
- Email notification settings
- System alert preferences
- Marketing communication controls
- Real-time notification management

## 🔧 Configuration Reference

### Environment Variables

| Variable | Description | Example | Required |
|----------|-------------|---------|----------|
| `MAIL_SERVER` | SMTP server for emails | `smtp.gmail.com` | Yes |
| `SENDGRID_API_KEY` | SendGrid API key | `SG.xxx` | No |
| `STRIPE_SECRET_KEY` | Stripe secret key | `sk_test_xxx` | Yes (Premium) |
| `UPLOAD_FOLDER` | File upload directory | `uploads` | Yes |
| `MAX_CONTENT_LENGTH` | Max upload size | `********` | No |

### API Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/account/profile` | Get user profile | JWT |
| `PUT` | `/api/account/profile` | Update user profile | JWT |
| `POST` | `/api/account/change-password` | Change password | JWT |
| `POST` | `/api/account/enable-2fa` | Enable 2FA | JWT |
| `GET` | `/api/account/subscription` | Get subscription details | JWT |
| `POST` | `/api/account/subscribe` | Create subscription | JWT |
| `DELETE` | `/api/account/delete` | Delete account | JWT |

## 🐛 Troubleshooting

### Common Issues

1. **Email Not Sending**:
   ```
   Error: SMTP authentication failed
   ```
   **Solution**: Check email credentials and app passwords

2. **File Upload Fails**:
   ```
   Error: File too large
   ```
   **Solution**: Check `MAX_CONTENT_LENGTH` setting

3. **Payment Processing Error**:
   ```
   Error: Invalid Stripe key
   ```
   **Solution**: Verify Stripe API keys in environment

4. **2FA Setup Issues**:
   ```
   Error: Invalid TOTP token
   ```
   **Solution**: Ensure time synchronization on devices

## 🔄 Integration with Previous US Features

US-10 integrates with all previous user stories:

- **US-01**: Enhanced user registration and profile management
- **US-02**: Advanced authentication with 2FA and security settings
- **US-03**: File management for profile pictures and document storage
- **US-04**: Job preference settings and saved searches
- **US-05**: NLP processing preferences and language settings
- **US-06**: Matching algorithm preferences and score history
- **US-07**: Suggestion preferences and AI model settings
- **US-08**: Dashboard customization and analytics preferences
- **US-09**: Security settings and API key management

## 📖 Additional Resources

- [Flask-Mail Documentation](https://flask-mail.readthedocs.io/)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [GDPR Compliance Guide](https://gdpr.eu/)
- [Two-Factor Authentication Best Practices](https://auth0.com/blog/a-complete-guide-to-two-factor-authentication/)
- [Password Security Guidelines](https://owasp.org/www-project-cheat-sheets/cheatsheets/Authentication_Cheat_Sheet.html)

---

**🎉 Congratulations!** You've successfully completed the entire Dr. Resume - AI Resume Scanner application with all 10 user stories implemented. This comprehensive system provides users with complete control over their account, privacy, and subscription management while maintaining the highest security standards.
