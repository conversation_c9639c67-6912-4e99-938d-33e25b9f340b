"""
US-09: Security Model
=====================

This module defines the database models for the API protection feature in the Dr. Resume application.
It includes models for security events, rate limiting, API keys, and audit logging.

Models:
- SecurityEvent: Security incidents and threats tracking
- RateLimitRecord: Rate limiting and throttling records
- APIKey: API key management for external integrations
- AuditLog: Comprehensive audit logging
- SecurityRule: Configurable security rules and policies

Author: Dr. <PERSON>sume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import uuid
from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.dialects.postgresql import UUID, JSON, INET
from enum import Enum
import hashlib
import secrets

# Initialize SQLAlchemy (will be configured in main app)
db = SQLAlchemy()


class SecurityEventType(Enum):
    """Enumeration for different types of security events"""
    BRUTE_FORCE_ATTEMPT = "brute_force_attempt"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INVALID_TOKEN = "invalid_token"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    SQL_INJECTION_ATTEMPT = "sql_injection_attempt"
    XSS_ATTEMPT = "xss_attempt"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    API_ABUSE = "api_abuse"
    MALFORMED_REQUEST = "malformed_request"
    GEOLOCATION_VIOLATION = "geolocation_violation"
    DEVICE_FINGERPRINT_MISMATCH = "device_fingerprint_mismatch"
    ACCOUNT_TAKEOVER_ATTEMPT = "account_takeover_attempt"


class SecurityEventSeverity(Enum):
    """Enumeration for security event severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class APIKeyStatus(Enum):
    """Enumeration for API key status"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    REVOKED = "revoked"
    EXPIRED = "expired"


class SecurityRule(db.Model):
    """
    Model for configurable security rules and policies
    """
    __tablename__ = 'security_rules'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Rule identification
    rule_name = db.Column(db.String(100), nullable=False, unique=True)
    rule_description = db.Column(db.Text)
    rule_category = db.Column(db.String(50), nullable=False)  # rate_limit, authentication, etc.
    
    # Rule configuration
    rule_config = db.Column(JSON, default=dict)
    is_enabled = db.Column(db.Boolean, default=True)
    priority = db.Column(db.Integer, default=100)
    
    # Rate limiting specific
    max_requests = db.Column(db.Integer)
    time_window_seconds = db.Column(db.Integer)
    
    # IP and geolocation rules
    allowed_ip_ranges = db.Column(JSON, default=list)
    blocked_ip_ranges = db.Column(JSON, default=list)
    allowed_countries = db.Column(JSON, default=list)
    blocked_countries = db.Column(JSON, default=list)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<SecurityRule {self.rule_name}>'
    
    def to_dict(self):
        """Convert security rule to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'rule_name': self.rule_name,
            'rule_description': self.rule_description,
            'rule_category': self.rule_category,
            'rule_config': self.rule_config,
            'is_enabled': self.is_enabled,
            'priority': self.priority,
            'max_requests': self.max_requests,
            'time_window_seconds': self.time_window_seconds,
            'allowed_ip_ranges': self.allowed_ip_ranges,
            'blocked_ip_ranges': self.blocked_ip_ranges,
            'allowed_countries': self.allowed_countries,
            'blocked_countries': self.blocked_countries,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class SecurityEvent(db.Model):
    """
    Model for tracking security events and incidents
    
    This model logs all security-related events for monitoring and analysis
    """
    __tablename__ = 'security_events'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Event classification
    event_type = db.Column(db.Enum(SecurityEventType), nullable=False)
    severity = db.Column(db.Enum(SecurityEventSeverity), default=SecurityEventSeverity.MEDIUM)
    
    # Request information
    ip_address = db.Column(INET, nullable=False)
    user_agent = db.Column(db.String(500))
    request_method = db.Column(db.String(10))
    request_path = db.Column(db.String(500))
    request_headers = db.Column(JSON, default=dict)
    request_body = db.Column(db.Text)
    
    # User context
    user_id = db.Column(UUID(as_uuid=True), nullable=True)
    session_id = db.Column(db.String(100))
    
    # Event details
    event_description = db.Column(db.Text, nullable=False)
    event_data = db.Column(JSON, default=dict)
    
    # Geolocation information
    country_code = db.Column(db.String(2))
    city = db.Column(db.String(100))
    latitude = db.Column(db.Float)
    longitude = db.Column(db.Float)
    
    # Response information
    response_status_code = db.Column(db.Integer)
    response_time_ms = db.Column(db.Float)
    
    # Security analysis
    threat_score = db.Column(db.Float, default=0.0)  # 0.0 to 1.0
    is_blocked = db.Column(db.Boolean, default=False)
    block_reason = db.Column(db.String(200))
    
    # Investigation status
    is_investigated = db.Column(db.Boolean, default=False)
    investigation_notes = db.Column(db.Text)
    investigated_by = db.Column(db.String(100))
    investigated_at = db.Column(db.DateTime)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        return f'<SecurityEvent {self.event_type.value}: {self.ip_address}>'
    
    def to_dict(self):
        """Convert security event to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'event_type': self.event_type.value,
            'severity': self.severity.value,
            'ip_address': str(self.ip_address),
            'user_agent': self.user_agent,
            'request_method': self.request_method,
            'request_path': self.request_path,
            'user_id': str(self.user_id) if self.user_id else None,
            'session_id': self.session_id,
            'event_description': self.event_description,
            'event_data': self.event_data,
            'country_code': self.country_code,
            'city': self.city,
            'threat_score': self.threat_score,
            'is_blocked': self.is_blocked,
            'block_reason': self.block_reason,
            'is_investigated': self.is_investigated,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class RateLimitRecord(db.Model):
    """
    Model for tracking rate limiting records
    
    This model stores rate limiting data for different endpoints and users
    """
    __tablename__ = 'rate_limit_records'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Rate limit identifier
    identifier = db.Column(db.String(200), nullable=False, index=True)  # IP, user_id, or API key
    identifier_type = db.Column(db.String(20), nullable=False)  # ip, user, api_key
    
    # Endpoint information
    endpoint = db.Column(db.String(200), nullable=False)
    method = db.Column(db.String(10), nullable=False)
    
    # Rate limit data
    request_count = db.Column(db.Integer, default=1)
    window_start = db.Column(db.DateTime, nullable=False)
    window_end = db.Column(db.DateTime, nullable=False)
    
    # Limit configuration
    max_requests = db.Column(db.Integer, nullable=False)
    window_duration_seconds = db.Column(db.Integer, nullable=False)
    
    # Status
    is_exceeded = db.Column(db.Boolean, default=False)
    first_request_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_request_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<RateLimitRecord {self.identifier}: {self.request_count}/{self.max_requests}>'
    
    def is_limit_exceeded(self):
        """Check if rate limit is exceeded"""
        return self.request_count >= self.max_requests
    
    def time_until_reset(self):
        """Calculate time until rate limit resets"""
        if datetime.utcnow() > self.window_end:
            return timedelta(0)
        return self.window_end - datetime.utcnow()
    
    def to_dict(self):
        """Convert rate limit record to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'identifier': self.identifier,
            'identifier_type': self.identifier_type,
            'endpoint': self.endpoint,
            'method': self.method,
            'request_count': self.request_count,
            'max_requests': self.max_requests,
            'window_duration_seconds': self.window_duration_seconds,
            'is_exceeded': self.is_exceeded,
            'time_until_reset_seconds': self.time_until_reset().total_seconds(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class APIKey(db.Model):
    """
    Model for API key management
    
    This model manages API keys for external integrations and premium features
    """
    __tablename__ = 'api_keys'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User information
    user_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # API key information
    key_name = db.Column(db.String(100), nullable=False)
    key_description = db.Column(db.Text)
    key_hash = db.Column(db.String(128), nullable=False, unique=True)  # SHA-256 hash
    key_prefix = db.Column(db.String(10), nullable=False)  # First 8 chars for identification
    
    # Permissions and scope
    scopes = db.Column(JSON, default=list)  # List of allowed scopes
    allowed_endpoints = db.Column(JSON, default=list)  # Specific endpoints allowed
    
    # Rate limiting
    rate_limit_per_hour = db.Column(db.Integer, default=1000)
    rate_limit_per_day = db.Column(db.Integer, default=10000)
    
    # Status and lifecycle
    status = db.Column(db.Enum(APIKeyStatus), default=APIKeyStatus.ACTIVE)
    expires_at = db.Column(db.DateTime)
    
    # Usage tracking
    total_requests = db.Column(db.Integer, default=0)
    last_used_at = db.Column(db.DateTime)
    last_used_ip = db.Column(INET)
    
    # Security
    allowed_ips = db.Column(JSON, default=list)  # IP whitelist
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<APIKey {self.key_prefix}*****: {self.status.value}>'
    
    @staticmethod
    def generate_api_key():
        """Generate a new API key"""
        key = secrets.token_urlsafe(32)
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        key_prefix = key[:8]
        return key, key_hash, key_prefix
    
    def verify_key(self, provided_key: str) -> bool:
        """Verify if provided key matches this API key"""
        provided_hash = hashlib.sha256(provided_key.encode()).hexdigest()
        return provided_hash == self.key_hash
    
    def is_valid(self) -> bool:
        """Check if API key is valid and not expired"""
        if self.status != APIKeyStatus.ACTIVE:
            return False
        
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False
        
        return True
    
    def update_usage(self, ip_address: str):
        """Update usage statistics"""
        self.total_requests += 1
        self.last_used_at = datetime.utcnow()
        self.last_used_ip = ip_address
    
    def to_dict(self, include_sensitive=False):
        """Convert API key to dictionary for JSON serialization"""
        data = {
            'id': str(self.id),
            'user_id': str(self.user_id),
            'key_name': self.key_name,
            'key_description': self.key_description,
            'key_prefix': self.key_prefix,
            'scopes': self.scopes,
            'allowed_endpoints': self.allowed_endpoints,
            'rate_limit_per_hour': self.rate_limit_per_hour,
            'rate_limit_per_day': self.rate_limit_per_day,
            'status': self.status.value,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'total_requests': self.total_requests,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'last_used_ip': str(self.last_used_ip) if self.last_used_ip else None,
            'allowed_ips': self.allowed_ips,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_sensitive:
            data['key_hash'] = self.key_hash
        
        return data


class AuditLog(db.Model):
    """
    Model for comprehensive audit logging
    
    This model tracks all significant actions and changes in the system
    """
    __tablename__ = 'audit_logs'
    
    # Primary key
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # User and session information
    user_id = db.Column(UUID(as_uuid=True), nullable=True)
    session_id = db.Column(db.String(100))
    ip_address = db.Column(INET)
    user_agent = db.Column(db.String(500))
    
    # Action information
    action = db.Column(db.String(100), nullable=False)
    resource_type = db.Column(db.String(50))
    resource_id = db.Column(db.String(100))
    
    # Request details
    request_method = db.Column(db.String(10))
    request_path = db.Column(db.String(500))
    request_params = db.Column(JSON, default=dict)
    
    # Change tracking
    old_values = db.Column(JSON, default=dict)
    new_values = db.Column(JSON, default=dict)
    
    # Result information
    success = db.Column(db.Boolean, nullable=False)
    error_message = db.Column(db.Text)
    response_status_code = db.Column(db.Integer)
    
    # Additional context
    additional_data = db.Column(JSON, default=dict)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        return f'<AuditLog {self.action}: {self.user_id}>'
    
    def to_dict(self):
        """Convert audit log to dictionary for JSON serialization"""
        return {
            'id': str(self.id),
            'user_id': str(self.user_id) if self.user_id else None,
            'session_id': self.session_id,
            'ip_address': str(self.ip_address) if self.ip_address else None,
            'user_agent': self.user_agent,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'request_method': self.request_method,
            'request_path': self.request_path,
            'request_params': self.request_params,
            'old_values': self.old_values,
            'new_values': self.new_values,
            'success': self.success,
            'error_message': self.error_message,
            'response_status_code': self.response_status_code,
            'additional_data': self.additional_data,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
