"""
US-09: Security Middleware
==========================

This module provides security middleware for API protection in the Dr. Resume application.
It includes request filtering, threat detection, rate limiting, and security monitoring.

Classes:
- SecurityMiddleware: Main security middleware
- SecurityHeaders: Security headers management
- RequestLogger: Security-focused request logging
- ThreatBlocker: Automatic threat blocking

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable
from functools import wraps
import ipaddress

from flask import Flask, request, jsonify, g, current_app
from werkzeug.exceptions import TooManyRequests, Forbidden, BadRequest

# Local imports
from us09_security_model import (
    db, SecurityEvent, SecurityEventType, SecurityEventSeverity,
    RateLimitRecord, AuditLog
)
from us09_security_service import ThreatDetector, RateLimiter, InputValidator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SecurityHeaders:
    """Security headers management"""
    
    @staticmethod
    def apply_security_headers(response):
        """Apply comprehensive security headers to response"""
        
        # Content Security Policy
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://cdnjs.cloudflare.com; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
        response.headers['Content-Security-Policy'] = csp_policy
        
        # Security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
        
        # API-specific headers
        response.headers['X-API-Version'] = '1.0'
        response.headers['X-Rate-Limit-Policy'] = 'standard'
        
        # Remove server information
        response.headers.pop('Server', None)
        
        return response


class RequestLogger:
    """Security-focused request logging"""
    
    def __init__(self):
        self.sensitive_fields = ['password', 'token', 'api_key', 'secret']
    
    def log_request(self, request_data: Dict, response_data: Dict = None, 
                   security_event: SecurityEvent = None):
        """Log request with security context"""
        
        # Sanitize sensitive data
        sanitized_request = self._sanitize_data(request_data)
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'ip_address': request_data.get('ip_address'),
            'method': request_data.get('method'),
            'path': request_data.get('path'),
            'user_agent': request_data.get('user_agent'),
            'request_size': len(str(request_data.get('body', ''))),
            'response_status': response_data.get('status_code') if response_data else None,
            'response_time_ms': response_data.get('response_time_ms') if response_data else None,
            'security_event_id': str(security_event.id) if security_event else None
        }
        
        # Log based on severity
        if security_event and security_event.severity in [SecurityEventSeverity.HIGH, SecurityEventSeverity.CRITICAL]:
            logger.warning(f"Security Event: {json.dumps(log_entry)}")
        else:
            logger.info(f"Request: {json.dumps(log_entry)}")
    
    def _sanitize_data(self, data: Dict) -> Dict:
        """Remove sensitive information from log data"""
        sanitized = data.copy()
        
        for field in self.sensitive_fields:
            if field in sanitized:
                sanitized[field] = '[REDACTED]'
        
        # Sanitize nested dictionaries
        for key, value in sanitized.items():
            if isinstance(value, dict):
                sanitized[key] = self._sanitize_data(value)
        
        return sanitized


class ThreatBlocker:
    """Automatic threat blocking"""
    
    def __init__(self):
        self.blocked_ips = set()
        self.blocked_user_agents = set()
        self.auto_block_threshold = 0.8
    
    def should_block_request(self, request_data: Dict, threat_analysis: Dict) -> Dict:
        """Determine if request should be blocked"""
        
        ip_address = request_data.get('ip_address')
        user_agent = request_data.get('user_agent')
        threat_score = threat_analysis.get('threat_score', 0.0)
        
        # Check if IP is already blocked
        if ip_address in self.blocked_ips:
            return {
                'should_block': True,
                'reason': 'IP address is blocked',
                'block_type': 'ip_blocked'
            }
        
        # Check if user agent is blocked
        if user_agent in self.blocked_user_agents:
            return {
                'should_block': True,
                'reason': 'User agent is blocked',
                'block_type': 'user_agent_blocked'
            }
        
        # Check threat score
        if threat_score >= self.auto_block_threshold:
            return {
                'should_block': True,
                'reason': f'High threat score: {threat_score}',
                'block_type': 'threat_score'
            }
        
        # Check for specific threat patterns
        threats = threat_analysis.get('threats_detected', [])
        critical_threats = ['sql_injection_in_url', 'sql_injection_in_body', 'xss_in_body']
        
        if any(threat in threats for threat in critical_threats):
            return {
                'should_block': True,
                'reason': 'Critical threat detected',
                'block_type': 'critical_threat'
            }
        
        return {
            'should_block': False,
            'reason': None,
            'block_type': None
        }
    
    def block_ip(self, ip_address: str, reason: str):
        """Add IP to block list"""
        self.blocked_ips.add(ip_address)
        logger.warning(f"Blocked IP {ip_address}: {reason}")
    
    def block_user_agent(self, user_agent: str, reason: str):
        """Add user agent to block list"""
        self.blocked_user_agents.add(user_agent)
        logger.warning(f"Blocked User Agent {user_agent}: {reason}")


class SecurityMiddleware:
    """Main security middleware"""
    
    def __init__(self, app: Flask = None):
        """Initialize security middleware"""
        self.threat_detector = ThreatDetector()
        self.rate_limiter = RateLimiter()
        self.input_validator = InputValidator()
        self.request_logger = RequestLogger()
        self.threat_blocker = ThreatBlocker()
        
        if app:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """Initialize middleware with Flask app"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        app.teardown_appcontext(self.teardown_request)
        
        # Register error handlers
        app.errorhandler(429)(self.handle_rate_limit_exceeded)
        app.errorhandler(403)(self.handle_forbidden)
        app.errorhandler(400)(self.handle_bad_request)
    
    def before_request(self):
        """Process request before handling"""
        start_time = time.time()
        g.request_start_time = start_time
        
        # Skip security checks for health endpoints
        if request.path in ['/health', '/api/health']:
            return
        
        # Collect request data
        request_data = self._collect_request_data()
        g.request_data = request_data
        
        # Validate input
        validation_result = self.input_validator.validate_request(request_data)
        if not validation_result['is_valid']:
            self._log_security_event(
                SecurityEventType.MALFORMED_REQUEST,
                SecurityEventSeverity.MEDIUM,
                f"Input validation failed: {validation_result['errors']}",
                request_data
            )
            return jsonify({
                'success': False,
                'message': 'Invalid request data',
                'errors': validation_result['errors']
            }), 400
        
        # Threat detection
        threat_analysis = self.threat_detector.analyze_request(request_data)
        g.threat_analysis = threat_analysis
        
        # Check if request should be blocked
        block_decision = self.threat_blocker.should_block_request(request_data, threat_analysis)
        
        if block_decision['should_block']:
            self._log_security_event(
                SecurityEventType.SUSPICIOUS_ACTIVITY,
                SecurityEventSeverity.HIGH,
                f"Request blocked: {block_decision['reason']}",
                request_data,
                threat_score=threat_analysis['threat_score'],
                is_blocked=True,
                block_reason=block_decision['reason']
            )
            
            return jsonify({
                'success': False,
                'message': 'Request blocked for security reasons',
                'error': 'security_violation'
            }), 403
        
        # Rate limiting
        ip_address = request_data['ip_address']
        user_id = getattr(g, 'current_user_id', None)
        
        # Check IP-based rate limit
        ip_limit_result = self.rate_limiter.check_rate_limit(
            ip_address, 'ip', request.path, request.method
        )
        
        if not ip_limit_result['allowed']:
            self._log_security_event(
                SecurityEventType.RATE_LIMIT_EXCEEDED,
                SecurityEventSeverity.MEDIUM,
                f"IP rate limit exceeded: {ip_address}",
                request_data
            )
            
            response = jsonify({
                'success': False,
                'message': 'Rate limit exceeded',
                'error': 'rate_limit_exceeded',
                'retry_after': ip_limit_result['retry_after']
            })
            response.status_code = 429
            response.headers['Retry-After'] = str(int(ip_limit_result['retry_after']))
            return response
        
        # Check user-based rate limit if authenticated
        if user_id:
            user_limit_result = self.rate_limiter.check_rate_limit(
                user_id, 'user', request.path, request.method
            )
            
            if not user_limit_result['allowed']:
                self._log_security_event(
                    SecurityEventType.RATE_LIMIT_EXCEEDED,
                    SecurityEventSeverity.MEDIUM,
                    f"User rate limit exceeded: {user_id}",
                    request_data
                )
                
                response = jsonify({
                    'success': False,
                    'message': 'User rate limit exceeded',
                    'error': 'rate_limit_exceeded',
                    'retry_after': user_limit_result['retry_after']
                })
                response.status_code = 429
                response.headers['Retry-After'] = str(int(user_limit_result['retry_after']))
                return response
        
        # Log potential threats (but don't block)
        if threat_analysis['threat_score'] > 0.3:
            severity = SecurityEventSeverity.HIGH if threat_analysis['threat_score'] > 0.7 else SecurityEventSeverity.MEDIUM
            self._log_security_event(
                SecurityEventType.SUSPICIOUS_ACTIVITY,
                severity,
                f"Potential threat detected: {threat_analysis['threats_detected']}",
                request_data,
                threat_score=threat_analysis['threat_score']
            )
    
    def after_request(self, response):
        """Process response after handling"""
        
        # Apply security headers
        response = SecurityHeaders.apply_security_headers(response)
        
        # Calculate response time
        if hasattr(g, 'request_start_time'):
            response_time = (time.time() - g.request_start_time) * 1000
            response.headers['X-Response-Time'] = f"{response_time:.2f}ms"
        
        # Log request
        if hasattr(g, 'request_data'):
            response_data = {
                'status_code': response.status_code,
                'response_time_ms': response_time if 'response_time' in locals() else None
            }
            
            security_event = getattr(g, 'security_event', None)
            self.request_logger.log_request(g.request_data, response_data, security_event)
        
        return response
    
    def teardown_request(self, exception):
        """Clean up after request"""
        # Commit any pending database changes
        try:
            db.session.commit()
        except Exception as e:
            logger.error(f"Error committing security data: {str(e)}")
            db.session.rollback()
    
    def _collect_request_data(self) -> Dict:
        """Collect comprehensive request data"""
        return {
            'ip_address': self._get_real_ip(),
            'method': request.method,
            'path': request.path,
            'url': request.url,
            'user_agent': request.headers.get('User-Agent', ''),
            'headers': dict(request.headers),
            'params': dict(request.args),
            'body': request.get_data(as_text=True),
            'json': request.get_json(silent=True) or {},
            'content_type': request.content_type,
            'content_length': request.content_length or 0
        }
    
    def _get_real_ip(self) -> str:
        """Get real IP address considering proxies"""
        # Check for forwarded headers
        forwarded_ips = [
            request.headers.get('X-Forwarded-For'),
            request.headers.get('X-Real-IP'),
            request.headers.get('X-Originating-IP')
        ]
        
        for ip_header in forwarded_ips:
            if ip_header:
                # Take the first IP if multiple are present
                ip = ip_header.split(',')[0].strip()
                try:
                    ipaddress.ip_address(ip)
                    return ip
                except ValueError:
                    continue
        
        return request.remote_addr or '127.0.0.1'
    
    def _log_security_event(self, event_type: SecurityEventType, severity: SecurityEventSeverity,
                           description: str, request_data: Dict, **kwargs):
        """Log security event to database"""
        try:
            event = SecurityEvent(
                event_type=event_type,
                severity=severity,
                ip_address=request_data['ip_address'],
                user_agent=request_data['user_agent'],
                request_method=request_data['method'],
                request_path=request_data['path'],
                request_headers=request_data['headers'],
                request_body=request_data['body'][:1000],  # Limit body size
                user_id=getattr(g, 'current_user_id', None),
                session_id=getattr(g, 'session_id', None),
                event_description=description,
                event_data=kwargs,
                threat_score=kwargs.get('threat_score', 0.0),
                is_blocked=kwargs.get('is_blocked', False),
                block_reason=kwargs.get('block_reason')
            )
            
            db.session.add(event)
            g.security_event = event
            
        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")
    
    # Error handlers
    def handle_rate_limit_exceeded(self, error):
        """Handle rate limit exceeded errors"""
        return jsonify({
            'success': False,
            'message': 'Rate limit exceeded',
            'error': 'rate_limit_exceeded'
        }), 429
    
    def handle_forbidden(self, error):
        """Handle forbidden errors"""
        return jsonify({
            'success': False,
            'message': 'Access forbidden',
            'error': 'forbidden'
        }), 403
    
    def handle_bad_request(self, error):
        """Handle bad request errors"""
        return jsonify({
            'success': False,
            'message': 'Bad request',
            'error': 'bad_request'
        }), 400


# Decorator for additional endpoint protection
def require_api_key(f):
    """Decorator to require API key for endpoint access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        
        if not api_key:
            return jsonify({
                'success': False,
                'message': 'API key required',
                'error': 'api_key_required'
            }), 401
        
        # Validate API key (implementation would check against database)
        # For now, just check if it's present
        
        return f(*args, **kwargs)
    
    return decorated_function


def require_premium_access(f):
    """Decorator to require premium access for endpoint"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check if user has premium access
        # Implementation would check user's subscription status
        
        return f(*args, **kwargs)
    
    return decorated_function
