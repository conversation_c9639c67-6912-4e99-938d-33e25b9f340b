# US-07: Suggestions (Basic + Premium OpenAPI) - Python Dependencies
# ===================================================================
# 
# This file contains all Python packages required for US-07 Suggestions feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01 through US-06, plus new OpenAPI requirements

# Core Flask Framework (from previous US)
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (from previous US)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration (from previous US)
Flask-CORS==4.0.0

# Environment Variables Management (from previous US)
python-dotenv==1.0.0

# Date and Time Utilities (from previous US)
python-dateutil==2.8.2

# JSON Handling and Validation (from previous US)
jsonschema==4.19.1

# File Processing (from US-03)
PyPDF2==3.0.1
python-docx==0.8.11

# Natural Language Processing Libraries (from US-05)
spacy==3.6.1
nltk==3.8.1
textblob==0.17.1
regex==2023.8.8

# Matching and Similarity Calculation Libraries (from US-06)
numpy==1.24.3
scipy==1.11.2
scikit-learn==1.3.0
pandas==2.0.3
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1
textdistance==4.6.0

# NEW: OpenAPI and AI Integration Libraries for US-07
# ===================================================

# OpenAI API for premium suggestions
openai==1.3.5

# HTTP client for API calls
httpx==0.25.0
aiohttp==3.8.6

# Async support for API calls
asyncio-throttle==1.0.2

# Rate limiting for API calls
ratelimit==2.2.1

# Retry logic for API failures
tenacity==8.2.3

# API response caching
requests-cache==1.1.1

# JSON Web Token handling for premium features
authlib==1.2.1

# Advanced text processing for suggestion generation
transformers==4.35.0
torch==2.1.0

# Template engine for suggestion formatting
jinja2==3.1.2

# Configuration management for API keys
configparser==6.0.0

# Secure credential storage
keyring==24.2.0

# API documentation generation
flask-restx==1.2.0

# Input validation for API requests
marshmallow==3.20.1
email-validator==2.0.0

# Logging and monitoring for API calls
structlog==23.2.0
colorlog==6.7.0

# Performance monitoring
flask-limiter==3.5.0

# Caching for suggestions
redis==5.0.1
flask-caching==2.1.0

# Background task processing for AI suggestions
celery==5.3.4

# Message queue for background tasks
kombu==5.3.4

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
pytest-asyncio==0.21.1
requests==2.31.0
responses==0.23.3

# Production Server (optional)
gunicorn==21.2.0

# Security
cryptography==41.0.4

# Memory profiling for AI operations (optional)
memory-profiler==0.61.0

# Additional text processing utilities (from US-05)
unidecode==1.3.6
langdetect==1.0.9

# Built-in Python modules (listed for clarity):
# collections - Specialized container datatypes (built-in)
# decimal - Decimal arithmetic (built-in)
# time - Time-related functions (built-in)
# typing - Type hints (built-in)
# re - Regular expressions (built-in)
# os - Operating system interface (built-in)
# sys - System-specific parameters (built-in)
# datetime - Date and time (built-in)
# json - JSON encoder/decoder (built-in)
# uuid - UUID generation (built-in)
# math - Mathematical functions (built-in)
# statistics - Statistical functions (built-in)
# asyncio - Asynchronous I/O (built-in)
# concurrent.futures - High-level interface for asynchronously executing callables (built-in)
