"""
US-10: Account Settings Flask Application
=========================================

Main Flask application for the account settings feature in the Dr. Resume application.
This app provides comprehensive user account management, profile settings, and preferences.

Features:
- Profile management with editable fields
- Email and password updates with re-authentication
- User preferences and notification settings
- Account security and privacy controls
- Subscription management
- Activity logging and audit trails

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import logging
from datetime import datetime, timedelta

from flask import Flask, jsonify, request
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager, create_access_token, jwt_required, get_jwt_identity
from flask_cors import CORS
from flask_mail import Mail
from dotenv import load_dotenv

# Local imports
from us10_account_model import db, UserProfile, UserPreferences, SubscriptionPlan, UserSubscription, AccountActivity
from us10_account_routes import account_bp

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_app(config_name='development'):
    """Create and configure the Flask application"""
    
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['SQLALCHEMY_DATABASE_URI'] = get_database_url()
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_ECHO'] = os.getenv('SQLALCHEMY_ECHO', 'False').lower() == 'true'
    
    # JWT Configuration
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', app.config['SECRET_KEY'])
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)
    app.config['JWT_ALGORITHM'] = 'HS256'
    
    # Mail Configuration
    app.config['MAIL_SERVER'] = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    app.config['MAIL_PORT'] = int(os.getenv('MAIL_PORT', 587))
    app.config['MAIL_USE_TLS'] = os.getenv('MAIL_USE_TLS', 'True').lower() == 'true'
    app.config['MAIL_USERNAME'] = os.getenv('MAIL_USERNAME')
    app.config['MAIL_PASSWORD'] = os.getenv('MAIL_PASSWORD')
    app.config['MAIL_DEFAULT_SENDER'] = os.getenv('MAIL_DEFAULT_SENDER', app.config['MAIL_USERNAME'])
    
    # File Upload Configuration
    app.config['UPLOAD_FOLDER'] = os.getenv('UPLOAD_FOLDER', 'uploads')
    app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    app.config['ALLOWED_EXTENSIONS'] = {'jpg', 'jpeg', 'png', 'gif'}
    
    # Account Settings Configuration
    app.config['PASSWORD_MIN_LENGTH'] = int(os.getenv('PASSWORD_MIN_LENGTH', 8))
    app.config['REQUIRE_EMAIL_VERIFICATION'] = os.getenv('REQUIRE_EMAIL_VERIFICATION', 'True').lower() == 'true'
    app.config['ACCOUNT_DELETION_GRACE_PERIOD'] = int(os.getenv('ACCOUNT_DELETION_GRACE_PERIOD', 30))  # days
    
    # Payment Configuration (for subscriptions)
    app.config['STRIPE_PUBLISHABLE_KEY'] = os.getenv('STRIPE_PUBLISHABLE_KEY')
    app.config['STRIPE_SECRET_KEY'] = os.getenv('STRIPE_SECRET_KEY')
    app.config['STRIPE_WEBHOOK_SECRET'] = os.getenv('STRIPE_WEBHOOK_SECRET')
    
    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)
    mail = Mail(app)
    
    # CORS Configuration
    cors_origins = os.getenv('CORS_ORIGINS', '*').split(',')
    CORS(app, origins=cors_origins, supports_credentials=True)
    
    # Register blueprints
    app.register_blueprint(account_bp)
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({
            'success': False,
            'message': 'Authorization token is required',
            'error': 'authorization_required'
        }), 401
    
    # Error handlers
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'success': False,
            'message': 'Bad request - invalid data provided',
            'error': 'bad_request'
        }), 400
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'success': False,
            'message': 'Resource not found',
            'error': 'not_found'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        logger.error(f"Internal server error: {str(error)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'internal_error'
        }), 500
    
    # Health check routes
    @app.route('/health', methods=['GET'])
    def health_check():
        """Application health check"""
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'healthy'
        except Exception as e:
            db_status = f'unhealthy: {str(e)}'
        
        return jsonify({
            'success': True,
            'message': 'US-10 Account Settings service is running',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'database': db_status,
            'features': {
                'profile_management': True,
                'password_changes': True,
                'email_updates': True,
                'preferences': True,
                'subscription_management': True,
                'activity_logging': True
            }
        }), 200
    
    @app.route('/api/health', methods=['GET'])
    def api_health_check():
        """API health check with account statistics"""
        try:
            # Get account statistics
            total_profiles = UserProfile.query.count()
            total_preferences = UserPreferences.query.count()
            active_subscriptions = UserSubscription.query.filter_by(status='active').count()
            
            return jsonify({
                'success': True,
                'message': 'API is healthy',
                'timestamp': datetime.utcnow().isoformat(),
                'service': 'US-10 Account Settings',
                'version': '1.0.0',
                'statistics': {
                    'total_profiles': total_profiles,
                    'total_preferences': total_preferences,
                    'active_subscriptions': active_subscriptions
                },
                'endpoints': {
                    'profile': '/api/account/profile',
                    'update_account': '/api/account/update_account',
                    'change_password': '/api/account/change-password',
                    'preferences': '/api/account/preferences',
                    'subscription': '/api/account/subscription'
                }
            }), 200
            
        except Exception as e:
            logger.error(f"API health check failed: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'API health check failed',
                'error': str(e)
            }), 500
    
    # Development routes (only in development mode)
    if os.getenv('FLASK_ENV') == 'development':
        @app.route('/api/dev/create-test-token', methods=['POST'])
        def create_test_token():
            """Create a test JWT token for development"""
            data = request.get_json() or {}
            user_id = data.get('user_id', 'test-user-123')
            is_premium = data.get('is_premium', False)
            
            additional_claims = {
                'role': 'premium' if is_premium else 'basic',
                'is_premium': is_premium
            }
            
            token = create_access_token(
                identity=user_id,
                additional_claims=additional_claims
            )
            
            return jsonify({
                'success': True,
                'message': 'Test token created',
                'token': token,
                'user_id': user_id,
                'is_premium': is_premium
            }), 200
        
        @app.route('/api/dev/create-sample-data', methods=['POST'])
        def create_sample_data():
            """Create sample account data for development"""
            try:
                user_id = 'test-user-123'
                
                # Create sample profile
                profile = UserProfile.query.filter_by(user_id=user_id).first()
                if not profile:
                    profile = UserProfile(
                        user_id=user_id,
                        first_name='John',
                        last_name='Doe',
                        display_name='John Doe',
                        bio='Software developer with 5 years of experience',
                        job_title='Senior Software Developer',
                        company='TechCorp Inc.',
                        industry='Technology',
                        experience_years=5
                    )
                    db.session.add(profile)
                
                # Create sample preferences
                preferences = UserPreferences.query.filter_by(user_id=user_id).first()
                if not preferences:
                    preferences = UserPreferences(
                        user_id=user_id,
                        email_notifications=True,
                        marketing_emails=False,
                        security_alerts=True,
                        theme='light',
                        items_per_page=20,
                        auto_save=True
                    )
                    db.session.add(preferences)
                
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': 'Sample data created successfully',
                    'data': {
                        'profile_id': str(profile.id),
                        'preferences_id': str(preferences.id)
                    }
                }), 200
                
            except Exception as e:
                logger.error(f"Error creating sample data: {str(e)}")
                db.session.rollback()
                return jsonify({
                    'success': False,
                    'message': 'Failed to create sample data',
                    'error': str(e)
                }), 500
    
    # Database initialization
    @app.before_first_request
    def create_tables():
        """Create database tables if they don't exist"""
        try:
            db.create_all()
            logger.info("Database tables created successfully")
            
            # Create default subscription plans
            create_default_subscription_plans()
            
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
    
    return app


def get_database_url():
    """Construct database URL from environment variables"""
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'dr_resume_db')
    db_user = os.getenv('DB_USER', 'dr_resume_user')
    db_password = os.getenv('DB_PASSWORD', 'password')
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"


def create_default_subscription_plans():
    """Create default subscription plans"""
    default_plans = [
        {
            'name': 'Basic',
            'description': 'Essential resume scanning features',
            'price_monthly': 0.00,
            'price_yearly': 0.00,
            'max_scans_per_month': 5,
            'ai_suggestions_included': False,
            'premium_templates': False,
            'priority_support': False,
            'advanced_analytics': False,
            'export_capabilities': False,
            'features': ['Basic resume scanning', 'Keyword matching', 'Basic suggestions'],
            'sort_order': 1
        },
        {
            'name': 'Premium',
            'description': 'Advanced features with AI-powered suggestions',
            'price_monthly': 9.99,
            'price_yearly': 99.99,
            'max_scans_per_month': 50,
            'ai_suggestions_included': True,
            'premium_templates': True,
            'priority_support': True,
            'advanced_analytics': True,
            'export_capabilities': True,
            'features': [
                'Unlimited resume scanning',
                'AI-powered suggestions',
                'Premium templates',
                'Advanced analytics',
                'Priority support',
                'Export capabilities'
            ],
            'sort_order': 2
        }
    ]
    
    for plan_data in default_plans:
        existing = SubscriptionPlan.query.filter_by(name=plan_data['name']).first()
        if not existing:
            plan = SubscriptionPlan(**plan_data)
            db.session.add(plan)
    
    try:
        db.session.commit()
        logger.info("Default subscription plans created")
    except Exception as e:
        logger.error(f"Error creating default subscription plans: {str(e)}")
        db.session.rollback()


# Create the Flask application
app = create_app()

if __name__ == '__main__':
    # Development server
    debug_mode = os.getenv('FLASK_ENV') == 'development'
    port = int(os.getenv('PORT', 5010))  # US-10 uses port 5010
    
    logger.info(f"Starting US-10 Account Settings service on port {port}")
    logger.info(f"Debug mode: {debug_mode}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode,
        threaded=True
    )
