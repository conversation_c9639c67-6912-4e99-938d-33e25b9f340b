"""
US-09: Authentication Decorators
================================

This module provides JWT-based authentication decorators and role-based access control
for the Dr. Resume application API protection.

Decorators:
- jwt_required_with_protection: Enhanced JWT protection with security logging
- require_premium_access: Premium role-based access control
- require_admin_access: Admin role-based access control
- log_api_access: API access logging decorator

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import logging
from functools import wraps
from datetime import datetime
from typing import Dict, Optional, Callable

from flask import request, jsonify, g
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt, verify_jwt_in_request
from werkzeug.exceptions import Forbidden, Unauthorized

# Local imports
from us09_security_model import db, SecurityEvent, SecurityEventType, SecurityEventSeverity, AuditLog

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def jwt_required_with_protection(optional: bool = False):
    """
    Enhanced JWT protection decorator with security logging
    
    Args:
        optional: If True, allows requests without tokens but still validates if present
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Verify JWT token
                if optional:
                    verify_jwt_in_request(optional=True)
                else:
                    verify_jwt_in_request()
                
                # Get user identity and claims
                user_id = get_jwt_identity()
                claims = get_jwt()
                
                # Store in Flask g for use in route
                g.current_user_id = user_id
                g.user_claims = claims
                g.is_authenticated = user_id is not None
                
                # Log successful authentication
                if user_id:
                    log_api_access(
                        user_id=user_id,
                        endpoint=request.endpoint,
                        method=request.method,
                        success=True
                    )
                
                return f(*args, **kwargs)
                
            except Exception as e:
                # Log failed authentication attempt
                log_security_event(
                    event_type=SecurityEventType.INVALID_TOKEN,
                    severity=SecurityEventSeverity.MEDIUM,
                    description=f"JWT authentication failed: {str(e)}",
                    user_id=None
                )
                
                return jsonify({
                    'success': False,
                    'message': 'Authentication required',
                    'error': 'invalid_token'
                }), 401
        
        return decorated_function
    return decorator


def require_premium_access(f: Callable) -> Callable:
    """
    Decorator to require premium access for endpoint
    
    Checks if user has premium role or is_premium flag in JWT claims
    """
    @wraps(f)
    @jwt_required_with_protection()
    def decorated_function(*args, **kwargs):
        try:
            claims = get_jwt()
            user_id = get_jwt_identity()
            
            # Check premium access
            is_premium = (
                claims.get('role') == 'premium' or 
                claims.get('is_premium', False) or
                claims.get('subscription_status') == 'active'
            )
            
            if not is_premium:
                # Log unauthorized premium access attempt
                log_security_event(
                    event_type=SecurityEventType.UNAUTHORIZED_ACCESS,
                    severity=SecurityEventSeverity.MEDIUM,
                    description="Attempted access to premium endpoint without premium subscription",
                    user_id=user_id
                )
                
                return jsonify({
                    'success': False,
                    'message': 'Premium subscription required',
                    'error': 'premium_required',
                    'upgrade_required': True,
                    'upgrade_url': '/api/account/subscribe'
                }), 403
            
            # Store premium status in g
            g.is_premium = True
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Error checking premium access: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'Access verification failed',
                'error': 'access_check_failed'
            }), 500
    
    return decorated_function


def require_admin_access(f: Callable) -> Callable:
    """
    Decorator to require admin access for endpoint
    
    Checks if user has admin role in JWT claims
    """
    @wraps(f)
    @jwt_required_with_protection()
    def decorated_function(*args, **kwargs):
        try:
            claims = get_jwt()
            user_id = get_jwt_identity()
            
            # Check admin access
            is_admin = (
                claims.get('role') == 'admin' or 
                claims.get('is_admin', False)
            )
            
            if not is_admin:
                # Log unauthorized admin access attempt
                log_security_event(
                    event_type=SecurityEventType.UNAUTHORIZED_ACCESS,
                    severity=SecurityEventSeverity.HIGH,
                    description="Attempted access to admin endpoint without admin privileges",
                    user_id=user_id
                )
                
                return jsonify({
                    'success': False,
                    'message': 'Administrator access required',
                    'error': 'admin_required'
                }), 403
            
            # Store admin status in g
            g.is_admin = True
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Error checking admin access: {str(e)}")
            return jsonify({
                'success': False,
                'message': 'Access verification failed',
                'error': 'access_check_failed'
            }), 500
    
    return decorated_function


def log_api_access(user_id: str, endpoint: str, method: str, success: bool = True, 
                  error_message: str = None):
    """
    Log API access for audit purposes
    
    Args:
        user_id: User ID making the request
        endpoint: API endpoint accessed
        method: HTTP method used
        success: Whether the access was successful
        error_message: Error message if access failed
    """
    try:
        audit_log = AuditLog(
            user_id=user_id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            action=f"{method} {endpoint}",
            resource_type='api_endpoint',
            resource_id=endpoint,
            request_method=method,
            request_path=request.path,
            request_params=dict(request.args),
            success=success,
            error_message=error_message,
            response_status_code=200 if success else 403
        )
        
        db.session.add(audit_log)
        db.session.commit()
        
    except Exception as e:
        logger.error(f"Failed to log API access: {str(e)}")


def log_security_event(event_type: SecurityEventType, severity: SecurityEventSeverity,
                      description: str, user_id: str = None, **kwargs):
    """
    Log security event
    
    Args:
        event_type: Type of security event
        severity: Severity level
        description: Event description
        user_id: User ID if applicable
        **kwargs: Additional event data
    """
    try:
        event = SecurityEvent(
            event_type=event_type,
            severity=severity,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_method=request.method,
            request_path=request.path,
            request_headers=dict(request.headers),
            user_id=user_id,
            event_description=description,
            event_data=kwargs
        )
        
        db.session.add(event)
        db.session.commit()
        
    except Exception as e:
        logger.error(f"Failed to log security event: {str(e)}")


def check_token_validity():
    """
    Check if current JWT token is valid
    
    Returns:
        Dict with token validity information
    """
    try:
        verify_jwt_in_request(optional=True)
        user_id = get_jwt_identity()
        claims = get_jwt()
        
        if user_id:
            return {
                'valid': True,
                'user_id': user_id,
                'claims': claims,
                'is_premium': (
                    claims.get('role') == 'premium' or 
                    claims.get('is_premium', False)
                ),
                'is_admin': (
                    claims.get('role') == 'admin' or 
                    claims.get('is_admin', False)
                )
            }
        else:
            return {
                'valid': False,
                'user_id': None,
                'claims': {},
                'is_premium': False,
                'is_admin': False
            }
            
    except Exception:
        return {
            'valid': False,
            'user_id': None,
            'claims': {},
            'is_premium': False,
            'is_admin': False
        }


# Route protection examples for different access levels
def protect_basic_route(f: Callable) -> Callable:
    """Basic route protection - requires valid JWT"""
    return jwt_required_with_protection()(f)


def protect_premium_route(f: Callable) -> Callable:
    """Premium route protection - requires premium subscription"""
    return require_premium_access(f)


def protect_admin_route(f: Callable) -> Callable:
    """Admin route protection - requires admin privileges"""
    return require_admin_access(f)


# Utility function to get current user info
def get_current_user_info() -> Dict:
    """
    Get current authenticated user information
    
    Returns:
        Dict with user information or None if not authenticated
    """
    if hasattr(g, 'current_user_id') and g.current_user_id:
        return {
            'user_id': g.current_user_id,
            'claims': getattr(g, 'user_claims', {}),
            'is_authenticated': getattr(g, 'is_authenticated', False),
            'is_premium': getattr(g, 'is_premium', False),
            'is_admin': getattr(g, 'is_admin', False)
        }
    
    return {
        'user_id': None,
        'claims': {},
        'is_authenticated': False,
        'is_premium': False,
        'is_admin': False
    }
