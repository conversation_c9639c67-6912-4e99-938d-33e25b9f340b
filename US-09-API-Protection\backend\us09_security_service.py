"""
US-09: Security Service
=======================

This module provides comprehensive security services for API protection in the Dr. Resume application.
It includes threat detection, rate limiting, input validation, and security monitoring.

Classes:
- SecurityService: Main security service coordinator
- ThreatDetector: Advanced threat detection and analysis
- RateLimiter: Intelligent rate limiting service
- InputValidator: Request validation and sanitization
- SecurityMonitor: Real-time security monitoring

Author: Dr. <PERSON>sume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import re
import json
import logging
import hashlib
import ipaddress
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict, deque
import time

# Security libraries
import bleach
from user_agents import parse as parse_user_agent
import geoip2.database
import geoip2.errors

# Flask and database
from flask import request, g
from sqlalchemy import func, and_, or_

# Local imports
from us09_security_model import (
    db, SecurityEvent, SecurityEventType, SecurityEventSeverity,
    RateLimitRecord, APIKey, AuditLog, SecurityRule
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ThreatDetector:
    """Advanced threat detection and analysis"""
    
    def __init__(self):
        """Initialize threat detector with patterns and rules"""
        self.sql_injection_patterns = [
            r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
            r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
            r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
            r"(--|#|/\*|\*/)",
            r"(\bUNION\s+SELECT\b)",
            r"(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)"
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>",
            r"<object[^>]*>",
            r"<embed[^>]*>",
            r"<link[^>]*>",
            r"<meta[^>]*>"
        ]
        
        self.suspicious_patterns = [
            r"\.\.\/",  # Directory traversal
            r"\/etc\/passwd",  # System file access
            r"\/proc\/",  # Process information
            r"cmd\.exe",  # Command execution
            r"powershell",  # PowerShell execution
            r"base64_decode",  # Encoded payloads
            r"eval\s*\(",  # Code evaluation
            r"exec\s*\("  # Code execution
        ]
        
        # Threat scoring weights
        self.threat_weights = {
            'sql_injection': 0.8,
            'xss_attempt': 0.7,
            'suspicious_pattern': 0.6,
            'brute_force': 0.9,
            'rate_limit_exceeded': 0.5,
            'geolocation_violation': 0.4,
            'malformed_request': 0.3
        }
    
    def analyze_request(self, request_data: Dict) -> Dict:
        """Analyze request for potential threats"""
        threat_score = 0.0
        threats_detected = []
        
        # Analyze URL and parameters
        url_threats = self._analyze_url(request_data.get('url', ''))
        threat_score += url_threats['score']
        threats_detected.extend(url_threats['threats'])
        
        # Analyze request body
        body_threats = self._analyze_body(request_data.get('body', ''))
        threat_score += body_threats['score']
        threats_detected.extend(body_threats['threats'])
        
        # Analyze headers
        header_threats = self._analyze_headers(request_data.get('headers', {}))
        threat_score += header_threats['score']
        threats_detected.extend(header_threats['threats'])
        
        # Analyze user agent
        ua_threats = self._analyze_user_agent(request_data.get('user_agent', ''))
        threat_score += ua_threats['score']
        threats_detected.extend(ua_threats['threats'])
        
        # Normalize threat score (0.0 to 1.0)
        threat_score = min(1.0, threat_score)
        
        return {
            'threat_score': threat_score,
            'threats_detected': threats_detected,
            'is_malicious': threat_score > 0.7,
            'should_block': threat_score > 0.8
        }
    
    def _analyze_url(self, url: str) -> Dict:
        """Analyze URL for threats"""
        threats = []
        score = 0.0
        
        # Check for SQL injection patterns
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                threats.append('sql_injection_in_url')
                score += self.threat_weights['sql_injection']
                break
        
        # Check for XSS patterns
        for pattern in self.xss_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                threats.append('xss_in_url')
                score += self.threat_weights['xss_attempt']
                break
        
        # Check for suspicious patterns
        for pattern in self.suspicious_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                threats.append('suspicious_pattern_in_url')
                score += self.threat_weights['suspicious_pattern']
                break
        
        return {'score': score, 'threats': threats}
    
    def _analyze_body(self, body: str) -> Dict:
        """Analyze request body for threats"""
        threats = []
        score = 0.0
        
        if not body:
            return {'score': 0.0, 'threats': []}
        
        # Check for SQL injection
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, body, re.IGNORECASE):
                threats.append('sql_injection_in_body')
                score += self.threat_weights['sql_injection']
                break
        
        # Check for XSS
        for pattern in self.xss_patterns:
            if re.search(pattern, body, re.IGNORECASE):
                threats.append('xss_in_body')
                score += self.threat_weights['xss_attempt']
                break
        
        return {'score': score, 'threats': threats}
    
    def _analyze_headers(self, headers: Dict) -> Dict:
        """Analyze request headers for threats"""
        threats = []
        score = 0.0
        
        # Check for suspicious headers
        suspicious_headers = ['x-forwarded-for', 'x-real-ip', 'x-originating-ip']
        for header in suspicious_headers:
            if header in headers:
                # Multiple proxy headers might indicate IP spoofing
                if len([h for h in suspicious_headers if h in headers]) > 1:
                    threats.append('potential_ip_spoofing')
                    score += 0.3
                    break
        
        # Check for malformed headers
        for key, value in headers.items():
            if len(str(value)) > 8192:  # Unusually long header
                threats.append('oversized_header')
                score += 0.2
                break
        
        return {'score': score, 'threats': threats}
    
    def _analyze_user_agent(self, user_agent: str) -> Dict:
        """Analyze user agent for threats"""
        threats = []
        score = 0.0
        
        if not user_agent:
            threats.append('missing_user_agent')
            score += 0.2
            return {'score': score, 'threats': threats}
        
        # Parse user agent
        try:
            parsed_ua = parse_user_agent(user_agent)
            
            # Check for bot patterns
            bot_patterns = ['bot', 'crawler', 'spider', 'scraper', 'scanner']
            if any(pattern in user_agent.lower() for pattern in bot_patterns):
                threats.append('bot_user_agent')
                score += 0.3
            
            # Check for suspicious patterns
            if len(user_agent) < 10 or len(user_agent) > 1000:
                threats.append('suspicious_user_agent_length')
                score += 0.2
            
        except Exception:
            threats.append('malformed_user_agent')
            score += 0.3
        
        return {'score': score, 'threats': threats}


class RateLimiter:
    """Intelligent rate limiting service"""
    
    def __init__(self):
        """Initialize rate limiter with default rules"""
        self.default_limits = {
            'global': {'requests': 1000, 'window': 3600},  # 1000 per hour
            'per_ip': {'requests': 100, 'window': 3600},   # 100 per hour per IP
            'per_user': {'requests': 500, 'window': 3600}, # 500 per hour per user
            'auth': {'requests': 10, 'window': 300},       # 10 auth attempts per 5 min
            'premium': {'requests': 50, 'window': 3600}    # 50 premium calls per hour
        }
        
        # In-memory cache for performance
        self.rate_cache = defaultdict(lambda: defaultdict(deque))
    
    def check_rate_limit(self, identifier: str, identifier_type: str, 
                        endpoint: str, method: str) -> Dict:
        """Check if request exceeds rate limits"""
        current_time = datetime.utcnow()
        
        # Get applicable limits
        limits = self._get_limits_for_endpoint(endpoint, method)
        
        for limit_name, limit_config in limits.items():
            # Check database record
            db_record = self._get_or_create_rate_record(
                identifier, identifier_type, endpoint, method,
                limit_config['requests'], limit_config['window']
            )
            
            # Check if limit is exceeded
            if db_record.is_limit_exceeded():
                # Check if window has expired
                if current_time > db_record.window_end:
                    # Reset the window
                    db_record.request_count = 1
                    db_record.window_start = current_time
                    db_record.window_end = current_time + timedelta(seconds=limit_config['window'])
                    db_record.is_exceeded = False
                else:
                    # Still within exceeded window
                    return {
                        'allowed': False,
                        'limit_exceeded': True,
                        'limit_name': limit_name,
                        'retry_after': db_record.time_until_reset().total_seconds(),
                        'current_count': db_record.request_count,
                        'max_requests': db_record.max_requests
                    }
            else:
                # Increment counter
                db_record.request_count += 1
                db_record.last_request_at = current_time
                
                # Check if limit is now exceeded
                if db_record.is_limit_exceeded():
                    db_record.is_exceeded = True
            
            db.session.commit()
        
        return {
            'allowed': True,
            'limit_exceeded': False,
            'current_count': db_record.request_count if 'db_record' in locals() else 0,
            'max_requests': db_record.max_requests if 'db_record' in locals() else 0
        }
    
    def _get_limits_for_endpoint(self, endpoint: str, method: str) -> Dict:
        """Get applicable rate limits for endpoint"""
        limits = {}
        
        # Default global limit
        limits['global'] = self.default_limits['global']
        
        # Endpoint-specific limits
        if '/auth/' in endpoint:
            limits['auth'] = self.default_limits['auth']
        elif '/premium-' in endpoint:
            limits['premium'] = self.default_limits['premium']
        
        return limits
    
    def _get_or_create_rate_record(self, identifier: str, identifier_type: str,
                                  endpoint: str, method: str, max_requests: int,
                                  window_seconds: int) -> RateLimitRecord:
        """Get or create rate limit record"""
        current_time = datetime.utcnow()
        window_start = current_time.replace(second=0, microsecond=0)
        window_end = window_start + timedelta(seconds=window_seconds)
        
        # Try to find existing record
        record = RateLimitRecord.query.filter_by(
            identifier=identifier,
            identifier_type=identifier_type,
            endpoint=endpoint,
            method=method
        ).filter(
            RateLimitRecord.window_end > current_time
        ).first()
        
        if not record:
            # Create new record
            record = RateLimitRecord(
                identifier=identifier,
                identifier_type=identifier_type,
                endpoint=endpoint,
                method=method,
                request_count=0,
                window_start=window_start,
                window_end=window_end,
                max_requests=max_requests,
                window_duration_seconds=window_seconds
            )
            db.session.add(record)
        
        return record


class InputValidator:
    """Request validation and sanitization"""
    
    def __init__(self):
        """Initialize input validator"""
        self.max_lengths = {
            'email': 254,
            'password': 128,
            'name': 100,
            'description': 1000,
            'url': 2048,
            'general': 500
        }
    
    def validate_request(self, request_data: Dict) -> Dict:
        """Validate and sanitize request data"""
        errors = []
        sanitized_data = {}
        
        # Validate JSON structure
        if 'json' in request_data:
            json_validation = self._validate_json(request_data['json'])
            errors.extend(json_validation['errors'])
            sanitized_data.update(json_validation['sanitized'])
        
        # Validate query parameters
        if 'params' in request_data:
            param_validation = self._validate_params(request_data['params'])
            errors.extend(param_validation['errors'])
        
        # Validate headers
        if 'headers' in request_data:
            header_validation = self._validate_headers(request_data['headers'])
            errors.extend(header_validation['errors'])
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'sanitized_data': sanitized_data
        }
    
    def _validate_json(self, json_data: Dict) -> Dict:
        """Validate JSON request body"""
        errors = []
        sanitized = {}
        
        for key, value in json_data.items():
            # Sanitize key
            clean_key = bleach.clean(str(key))
            
            # Validate and sanitize value
            if isinstance(value, str):
                # Check length
                max_length = self.max_lengths.get(key, self.max_lengths['general'])
                if len(value) > max_length:
                    errors.append(f"Field '{key}' exceeds maximum length of {max_length}")
                    continue
                
                # Sanitize HTML
                clean_value = bleach.clean(value)
                sanitized[clean_key] = clean_value
                
            elif isinstance(value, (int, float, bool)):
                sanitized[clean_key] = value
            elif isinstance(value, list):
                sanitized[clean_key] = [bleach.clean(str(item)) if isinstance(item, str) else item for item in value]
            else:
                sanitized[clean_key] = value
        
        return {'errors': errors, 'sanitized': sanitized}
    
    def _validate_params(self, params: Dict) -> Dict:
        """Validate query parameters"""
        errors = []
        
        for key, value in params.items():
            # Check for suspicious patterns
            if any(char in str(value) for char in ['<', '>', '"', "'"]):
                errors.append(f"Parameter '{key}' contains potentially dangerous characters")
        
        return {'errors': errors}
    
    def _validate_headers(self, headers: Dict) -> Dict:
        """Validate request headers"""
        errors = []
        
        # Check for required headers
        required_headers = ['user-agent']
        for header in required_headers:
            if header not in headers:
                errors.append(f"Missing required header: {header}")
        
        # Check header sizes
        for key, value in headers.items():
            if len(str(value)) > 8192:
                errors.append(f"Header '{key}' is too large")
        
        return {'errors': errors}
