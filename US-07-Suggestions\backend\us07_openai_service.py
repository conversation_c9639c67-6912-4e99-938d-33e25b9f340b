"""
US-07: OpenAI Premium Suggestions Service
=========================================

This module provides premium AI-powered suggestions using OpenAI API.
It generates detailed, personalized recommendations for resume improvement.

Classes:
- OpenAIService: Main service for interacting with OpenAI API
- PromptTemplates: Templates for generating effective prompts
- ResponseParser: Parser for OpenAI responses

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import time

# OpenAI and HTTP libraries
import openai
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential

# Configuration and environment
from dotenv import load_dotenv

# Local imports
from us07_suggestions_model import PremiumSuggestion

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PromptTemplates:
    """Templates for generating effective OpenAI prompts"""
    
    RESUME_ANALYSIS_PROMPT = """
    You are an expert career coach and resume optimization specialist with 15+ years of experience helping professionals land their dream jobs. 

    Please analyze the following resume and job description to provide detailed, actionable improvement suggestions.

    RESUME CONTENT:
    {resume_text}

    JOB DESCRIPTION:
    {job_description}

    CURRENT MATCHING SCORE: {matching_score}%

    Please provide a comprehensive analysis in the following JSON format:

    {{
        "overall_assessment": "Brief overall assessment of the resume's alignment with the job",
        "strengths": ["List of current strengths in the resume"],
        "improvement_areas": [
            {{
                "category": "Category name (e.g., Skills, Experience, Format)",
                "priority": "High/Medium/Low",
                "issue": "Specific issue identified",
                "recommendation": "Detailed recommendation",
                "implementation_steps": ["Step 1", "Step 2", "Step 3"],
                "expected_impact": "Estimated impact on matching score (percentage)",
                "timeline": "Estimated time to implement"
            }}
        ],
        "missing_keywords": ["List of important keywords missing from resume"],
        "keyword_optimization": [
            {{
                "keyword": "Specific keyword",
                "context": "Where/how to include it naturally",
                "importance": "High/Medium/Low"
            }}
        ],
        "industry_insights": {{
            "current_trends": ["Current industry trends relevant to this role"],
            "in_demand_skills": ["Skills that are highly valued in this industry"],
            "career_progression": "Advice for career advancement in this field"
        }},
        "ats_optimization": [
            "Specific tips for improving ATS compatibility"
        ],
        "personalized_tips": [
            "Personalized recommendations based on the specific resume and job"
        ]
    }}

    Focus on providing specific, actionable advice that will measurably improve the candidate's chances of getting an interview.
    """

    SKILL_ENHANCEMENT_PROMPT = """
    You are a technical skills assessment expert. Analyze the candidate's skills against the job requirements.

    CANDIDATE SKILLS: {candidate_skills}
    REQUIRED SKILLS: {required_skills}
    JOB LEVEL: {job_level}

    Provide detailed skill enhancement recommendations in JSON format:

    {{
        "skill_gaps": [
            {{
                "skill": "Skill name",
                "current_level": "Beginner/Intermediate/Advanced/Expert",
                "required_level": "Required level for the job",
                "learning_path": ["Step 1", "Step 2", "Step 3"],
                "resources": ["Resource 1", "Resource 2"],
                "timeline": "Estimated learning time"
            }}
        ],
        "skill_enhancements": [
            {{
                "existing_skill": "Skill the candidate already has",
                "enhancement": "How to improve or showcase it better",
                "certification_suggestions": ["Relevant certifications"],
                "project_ideas": ["Project ideas to demonstrate the skill"]
            }}
        ]
    }}
    """

    EXPERIENCE_OPTIMIZATION_PROMPT = """
    You are an executive resume writer specializing in quantifying achievements and impact.

    CURRENT EXPERIENCE SECTION:
    {experience_text}

    TARGET ROLE:
    {target_role}

    Please rewrite and optimize the experience section with:
    1. Quantified achievements
    2. Action verbs
    3. Relevant keywords
    4. Impact-focused language

    Provide response in JSON format:

    {{
        "optimized_experience": [
            {{
                "original": "Original bullet point",
                "optimized": "Improved version with metrics and impact",
                "improvement_explanation": "Why this version is better"
            }}
        ],
        "missing_elements": ["Elements that should be added"],
        "formatting_suggestions": ["Formatting improvements"]
    }}
    """


class OpenAIService:
    """Service for generating premium suggestions using OpenAI API"""
    
    def __init__(self):
        """Initialize the OpenAI service"""
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        # Configure OpenAI client
        openai.api_key = self.api_key
        self.client = openai.OpenAI(api_key=self.api_key)
        
        # Configuration
        self.model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        self.max_tokens = int(os.getenv('OPENAI_MAX_TOKENS', '2000'))
        self.temperature = float(os.getenv('OPENAI_TEMPERATURE', '0.7'))
        
        # Rate limiting
        self.requests_per_minute = int(os.getenv('OPENAI_RPM_LIMIT', '20'))
        self.last_request_time = 0
        
        # Cost tracking
        self.cost_per_token = {
            'gpt-3.5-turbo': {'input': 0.0015/1000, 'output': 0.002/1000},
            'gpt-4': {'input': 0.03/1000, 'output': 0.06/1000}
        }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def generate_premium_suggestions(self, resume_text: str, job_description: str, 
                                         matching_score: float, user_id: str) -> Dict:
        """Generate premium suggestions using OpenAI API"""
        try:
            # Rate limiting
            await self._enforce_rate_limit()
            
            # Prepare the prompt
            prompt = PromptTemplates.RESUME_ANALYSIS_PROMPT.format(
                resume_text=resume_text[:4000],  # Limit text length
                job_description=job_description[:2000],
                matching_score=matching_score
            )
            
            # Make API call
            start_time = time.time()
            response = await self._make_api_call(prompt)
            end_time = time.time()
            
            # Parse response
            suggestions_data = self._parse_response(response)
            
            # Calculate cost
            cost = self._calculate_cost(response)
            
            # Add metadata
            suggestions_data['metadata'] = {
                'model_used': self.model,
                'tokens_used': response.usage.total_tokens,
                'cost_estimate': cost,
                'response_time': end_time - start_time,
                'generated_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Generated premium suggestions for user {user_id}. Tokens: {response.usage.total_tokens}, Cost: ${cost:.4f}")
            
            return suggestions_data
            
        except Exception as e:
            logger.error(f"Error generating premium suggestions: {str(e)}")
            raise
    
    async def enhance_skill_recommendations(self, candidate_skills: List[str], 
                                          required_skills: List[str], job_level: str) -> Dict:
        """Generate enhanced skill recommendations"""
        try:
            await self._enforce_rate_limit()
            
            prompt = PromptTemplates.SKILL_ENHANCEMENT_PROMPT.format(
                candidate_skills=', '.join(candidate_skills),
                required_skills=', '.join(required_skills),
                job_level=job_level
            )
            
            response = await self._make_api_call(prompt)
            return self._parse_response(response)
            
        except Exception as e:
            logger.error(f"Error enhancing skill recommendations: {str(e)}")
            raise
    
    async def optimize_experience_section(self, experience_text: str, target_role: str) -> Dict:
        """Optimize experience section with AI assistance"""
        try:
            await self._enforce_rate_limit()
            
            prompt = PromptTemplates.EXPERIENCE_OPTIMIZATION_PROMPT.format(
                experience_text=experience_text[:2000],
                target_role=target_role
            )
            
            response = await self._make_api_call(prompt)
            return self._parse_response(response)
            
        except Exception as e:
            logger.error(f"Error optimizing experience section: {str(e)}")
            raise
    
    async def _make_api_call(self, prompt: str) -> object:
        """Make API call to OpenAI"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert career coach and resume optimization specialist."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                response_format={"type": "json_object"}
            )
            return response
            
        except openai.RateLimitError:
            logger.warning("OpenAI rate limit exceeded. Waiting...")
            await asyncio.sleep(60)
            raise
        except openai.APIError as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in API call: {str(e)}")
            raise
    
    def _parse_response(self, response: object) -> Dict:
        """Parse OpenAI response"""
        try:
            content = response.choices[0].message.content
            return json.loads(content)
        except (json.JSONDecodeError, KeyError, IndexError) as e:
            logger.error(f"Error parsing OpenAI response: {str(e)}")
            return {
                "error": "Failed to parse AI response",
                "raw_response": str(response)
            }
    
    def _calculate_cost(self, response: object) -> float:
        """Calculate the cost of the API call"""
        if self.model not in self.cost_per_token:
            return 0.0
        
        usage = response.usage
        input_cost = usage.prompt_tokens * self.cost_per_token[self.model]['input']
        output_cost = usage.completion_tokens * self.cost_per_token[self.model]['output']
        
        return input_cost + output_cost
    
    async def _enforce_rate_limit(self):
        """Enforce rate limiting for API calls"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        min_interval = 60.0 / self.requests_per_minute
        
        if time_since_last_request < min_interval:
            sleep_time = min_interval - time_since_last_request
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def validate_api_key(self) -> bool:
        """Validate the OpenAI API key"""
        try:
            response = self.client.models.list()
            return True
        except Exception as e:
            logger.error(f"API key validation failed: {str(e)}")
            return False


class ResponseParser:
    """Parser for OpenAI responses"""
    
    @staticmethod
    def extract_suggestions(ai_response: Dict) -> List[Dict]:
        """Extract structured suggestions from AI response"""
        suggestions = []
        
        improvement_areas = ai_response.get('improvement_areas', [])
        for area in improvement_areas:
            suggestion = {
                'title': f"Improve {area.get('category', 'Resume')}",
                'description': area.get('recommendation', ''),
                'priority': area.get('priority', 'Medium').lower(),
                'implementation_steps': area.get('implementation_steps', []),
                'expected_impact': area.get('expected_impact', ''),
                'timeline': area.get('timeline', ''),
                'category': area.get('category', 'General')
            }
            suggestions.append(suggestion)
        
        return suggestions
    
    @staticmethod
    def extract_keywords(ai_response: Dict) -> List[str]:
        """Extract missing keywords from AI response"""
        return ai_response.get('missing_keywords', [])
    
    @staticmethod
    def extract_industry_insights(ai_response: Dict) -> Dict:
        """Extract industry insights from AI response"""
        return ai_response.get('industry_insights', {})


# Global service instance
openai_service = None

def get_openai_service() -> OpenAIService:
    """Get or create OpenAI service instance"""
    global openai_service
    if openai_service is None:
        openai_service = OpenAIService()
    return openai_service
