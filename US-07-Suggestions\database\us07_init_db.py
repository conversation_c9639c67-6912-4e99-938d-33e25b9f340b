"""
US-07: Suggestions Database Initialization
==========================================

This script initializes the database for the suggestions feature in the Dr. Resume application.
It creates tables, indexes, and optionally inserts sample data for development and testing.

Usage:
    python us07_init_db.py [--sample-data] [--reset]

Options:
    --sample-data: Insert sample data for development
    --reset: Drop existing tables before creating new ones

Author: Dr. Resume Development Team
Date: 2025-07-23
Version: 1.0.0
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
import uuid

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseInitializer:
    """Database initialization class for US-07 Suggestions"""
    
    def __init__(self):
        """Initialize database connection parameters"""
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 5432)),
            'database': os.getenv('DB_NAME', 'dr_resume_db'),
            'user': os.getenv('DB_USER', 'dr_resume_user'),
            'password': os.getenv('DB_PASSWORD', 'password')
        }
        
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            self.cursor = self.connection.cursor()
            logger.info("Database connection established")
            return True
        except psycopg2.Error as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("Database connection closed")
    
    def execute_sql_file(self, file_path):
        """Execute SQL commands from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # Split by semicolon and execute each statement
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement:
                    try:
                        self.cursor.execute(statement)
                        logger.debug(f"Executed: {statement[:50]}...")
                    except psycopg2.Error as e:
                        logger.warning(f"Statement failed: {e}")
                        # Continue with other statements
            
            logger.info(f"Successfully executed SQL file: {file_path}")
            return True
            
        except FileNotFoundError:
            logger.error(f"SQL file not found: {file_path}")
            return False
        except Exception as e:
            logger.error(f"Error executing SQL file: {e}")
            return False
    
    def create_tables(self, reset=False):
        """Create database tables"""
        try:
            if reset:
                logger.info("Dropping existing tables...")
                self.drop_tables()
            
            # Get the directory of this script
            script_dir = os.path.dirname(os.path.abspath(__file__))
            schema_file = os.path.join(script_dir, 'us07_schema.sql')
            
            logger.info("Creating tables from schema file...")
            success = self.execute_sql_file(schema_file)
            
            if success:
                logger.info("Tables created successfully")
            else:
                logger.error("Failed to create tables")
            
            return success
            
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def drop_tables(self):
        """Drop existing tables"""
        try:
            drop_statements = [
                "DROP VIEW IF EXISTS premium_suggestions_summary CASCADE;",
                "DROP VIEW IF EXISTS active_suggestions CASCADE;",
                "DROP TABLE IF EXISTS premium_suggestions CASCADE;",
                "DROP TABLE IF EXISTS suggestions CASCADE;",
                "DROP TABLE IF EXISTS suggestion_categories CASCADE;",
                "DROP TYPE IF EXISTS suggestion_priority CASCADE;",
                "DROP TYPE IF EXISTS suggestion_type CASCADE;",
                "DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;"
            ]
            
            for statement in drop_statements:
                try:
                    self.cursor.execute(statement)
                    logger.debug(f"Executed: {statement}")
                except psycopg2.Error as e:
                    logger.debug(f"Drop statement failed (may not exist): {e}")
            
            logger.info("Existing tables dropped")
            
        except Exception as e:
            logger.error(f"Error dropping tables: {e}")
    
    def insert_sample_data(self):
        """Insert sample data for development and testing"""
        try:
            logger.info("Inserting sample data...")
            
            # Sample suggestion categories (already in schema file)
            logger.info("Suggestion categories already created by schema")
            
            # Sample basic suggestions
            sample_suggestions = [
                {
                    'id': str(uuid.uuid4()),
                    'user_id': 'sample-user-123',
                    'resume_id': 'sample-resume-456',
                    'job_description_id': 'sample-jd-789',
                    'suggestion_type': 'missing_keyword',
                    'priority': 'high',
                    'title': 'Add missing keyword: Python',
                    'description': 'The keyword "Python" appears 5 times in the job description but is missing from your resume.',
                    'missing_keywords': '["Python", "Django"]',
                    'suggested_keywords': '["Python", "Django", "Flask"]',
                    'confidence_score': 0.9,
                    'implementation_difficulty': 2,
                    'expected_impact': 15.0
                },
                {
                    'id': str(uuid.uuid4()),
                    'user_id': 'sample-user-123',
                    'resume_id': 'sample-resume-456',
                    'job_description_id': 'sample-jd-789',
                    'suggestion_type': 'skill_enhancement',
                    'priority': 'medium',
                    'title': 'Enhance programming skills section',
                    'description': 'Consider creating a dedicated section highlighting your programming expertise.',
                    'missing_keywords': '[]',
                    'suggested_keywords': '["programming", "software development", "coding"]',
                    'confidence_score': 0.7,
                    'implementation_difficulty': 3,
                    'expected_impact': 8.0
                },
                {
                    'id': str(uuid.uuid4()),
                    'user_id': 'sample-user-123',
                    'resume_id': 'sample-resume-456',
                    'job_description_id': 'sample-jd-789',
                    'suggestion_type': 'experience_improvement',
                    'priority': 'medium',
                    'title': 'Add quantifiable achievements',
                    'description': 'Your resume would be stronger with specific, measurable achievements.',
                    'missing_keywords': '[]',
                    'suggested_keywords': '["metrics", "achievements", "results"]',
                    'confidence_score': 0.8,
                    'implementation_difficulty': 4,
                    'expected_impact': 12.0
                }
            ]
            
            for suggestion in sample_suggestions:
                insert_query = """
                INSERT INTO suggestions (
                    id, user_id, resume_id, job_description_id, suggestion_type,
                    priority, title, description, missing_keywords, suggested_keywords,
                    confidence_score, implementation_difficulty, expected_impact
                ) VALUES (
                    %(id)s, %(user_id)s, %(resume_id)s, %(job_description_id)s, %(suggestion_type)s,
                    %(priority)s, %(title)s, %(description)s, %(missing_keywords)s::jsonb, %(suggested_keywords)s::jsonb,
                    %(confidence_score)s, %(implementation_difficulty)s, %(expected_impact)s
                ) ON CONFLICT DO NOTHING;
                """
                
                self.cursor.execute(insert_query, suggestion)
            
            # Sample premium suggestion
            premium_suggestion = {
                'id': str(uuid.uuid4()),
                'user_id': 'sample-user-123',
                'resume_id': 'sample-resume-456',
                'job_description_id': 'sample-jd-789',
                'ai_analysis': 'Your resume shows strong technical skills but could benefit from better keyword optimization and quantified achievements.',
                'detailed_recommendations': '''[
                    {
                        "category": "Skills",
                        "priority": "High",
                        "issue": "Missing key technologies",
                        "recommendation": "Add Python, Django, and PostgreSQL to your skills section",
                        "implementation_steps": ["Create a dedicated skills section", "List programming languages", "Include frameworks and databases"],
                        "expected_impact": "15%",
                        "timeline": "1-2 hours"
                    }
                ]''',
                'personalized_tips': '''[
                    "Highlight your 3 years of experience more prominently",
                    "Add specific project examples with technologies used",
                    "Include metrics like 'improved performance by 30%'"
                ]''',
                'industry_insights': '''{"current_trends": ["Remote work capabilities", "Cloud technologies", "Agile methodologies"], "in_demand_skills": ["Python", "React", "AWS"]}''',
                'ai_model_used': 'gpt-3.5-turbo',
                'ai_response_tokens': 450,
                'ai_cost_estimate': 0.0012,
                'is_generated': True,
                'generated_at': datetime.utcnow()
            }
            
            premium_insert_query = """
            INSERT INTO premium_suggestions (
                id, user_id, resume_id, job_description_id, ai_analysis,
                detailed_recommendations, personalized_tips, industry_insights,
                ai_model_used, ai_response_tokens, ai_cost_estimate,
                is_generated, generated_at
            ) VALUES (
                %(id)s, %(user_id)s, %(resume_id)s, %(job_description_id)s, %(ai_analysis)s,
                %(detailed_recommendations)s::jsonb, %(personalized_tips)s::jsonb, %(industry_insights)s::jsonb,
                %(ai_model_used)s, %(ai_response_tokens)s, %(ai_cost_estimate)s,
                %(is_generated)s, %(generated_at)s
            ) ON CONFLICT DO NOTHING;
            """
            
            self.cursor.execute(premium_insert_query, premium_suggestion)
            
            logger.info("Sample data inserted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting sample data: {e}")
            return False
    
    def verify_installation(self):
        """Verify that tables were created correctly"""
        try:
            # Check if tables exist
            tables_to_check = ['suggestions', 'premium_suggestions', 'suggestion_categories']
            
            for table in tables_to_check:
                self.cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = %s
                    );
                """, (table,))
                
                exists = self.cursor.fetchone()[0]
                if exists:
                    logger.info(f"✓ Table '{table}' exists")
                else:
                    logger.error(f"✗ Table '{table}' does not exist")
                    return False
            
            # Check record counts
            for table in tables_to_check:
                self.cursor.execute(f"SELECT COUNT(*) FROM {table};")
                count = self.cursor.fetchone()[0]
                logger.info(f"  - {table}: {count} records")
            
            logger.info("Database verification completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying installation: {e}")
            return False


def main():
    """Main function to initialize the database"""
    parser = argparse.ArgumentParser(description='Initialize US-07 Suggestions database')
    parser.add_argument('--sample-data', action='store_true', 
                       help='Insert sample data for development')
    parser.add_argument('--reset', action='store_true',
                       help='Drop existing tables before creating new ones')
    
    args = parser.parse_args()
    
    logger.info("Starting US-07 Suggestions database initialization...")
    
    # Initialize database
    db_init = DatabaseInitializer()
    
    try:
        # Connect to database
        if not db_init.connect():
            logger.error("Failed to connect to database")
            sys.exit(1)
        
        # Create tables
        if not db_init.create_tables(reset=args.reset):
            logger.error("Failed to create tables")
            sys.exit(1)
        
        # Insert sample data if requested
        if args.sample_data:
            if not db_init.insert_sample_data():
                logger.error("Failed to insert sample data")
                sys.exit(1)
        
        # Verify installation
        if not db_init.verify_installation():
            logger.error("Database verification failed")
            sys.exit(1)
        
        logger.info("✅ US-07 Suggestions database initialization completed successfully!")
        
        if args.sample_data:
            logger.info("📊 Sample data has been inserted for development")
        
        logger.info("🚀 You can now start the US-07 Suggestions service")
        
    except KeyboardInterrupt:
        logger.info("Initialization cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        db_init.disconnect()


if __name__ == '__main__':
    main()
