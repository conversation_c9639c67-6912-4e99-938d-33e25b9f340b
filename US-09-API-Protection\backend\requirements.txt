# US-09: API Protection - Python Dependencies
# ============================================
# 
# This file contains all Python packages required for US-09 API Protection feature
# Install with: pip install -r requirements.txt
# 
# Dependencies include packages from US-01 through US-08, plus new security requirements

# Core Flask Framework (from previous US)
Flask==2.3.3
Werkzeug==2.3.7

# Database ORM and PostgreSQL (from previous US)
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
psycopg2-binary==2.9.7

# JWT Authentication (from US-02)
Flask-JWT-Extended==4.5.3
PyJWT==2.8.0

# Password Hashing (from US-01)
bcrypt==4.0.1

# CORS Support for Frontend Integration (from previous US)
Flask-CORS==4.0.0

# Environment Variables Management (from previous US)
python-dotenv==1.0.0

# Date and Time Utilities (from previous US)
python-dateutil==2.8.2

# JSON Handling and Validation (from previous US)
jsonschema==4.19.1

# NEW: Security and API Protection Libraries for US-09
# ====================================================

# Advanced Rate Limiting
Flask-Limiter==3.5.0
redis==5.0.1

# API Security Headers
Flask-Talisman==1.1.0

# Request/Response Validation
marshmallow==3.20.1
marshmallow-sqlalchemy==0.29.0

# API Documentation and Validation
flask-restx==1.2.0
apispec==6.3.0

# Security Monitoring and Logging
structlog==23.2.0
colorlog==6.7.0

# IP Address Validation and Geolocation
ipaddress==1.0.23
geoip2==4.7.0
maxminddb==2.2.0

# Request Throttling and Circuit Breaker
tenacity==8.2.3
pybreaker==0.8.0

# API Key Management
secrets==1.0.0
cryptography==41.0.4

# Session Management and Security
Flask-Session==0.5.0
itsdangerous==2.1.2

# Input Sanitization
bleach==6.1.0
html5lib==1.1

# SQL Injection Protection (additional to SQLAlchemy)
sqlparse==0.4.4

# Cross-Site Request Forgery Protection
Flask-WTF==1.2.1
WTForms==3.1.0

# Content Security Policy
flask-csp==1.0.0

# API Versioning
flask-restful==0.3.10

# Request ID Tracking
flask-uuid==0.2

# Audit Logging
flask-audit==0.1.0

# API Analytics and Monitoring
prometheus-flask-exporter==0.23.0

# Error Tracking
sentry-sdk[flask]==1.38.0

# HTTP Security Headers
secure==0.3.0

# Bot Detection and CAPTCHA
flask-recaptcha==0.4.2

# API Gateway Features
flask-httpauth==4.8.0

# Advanced Authentication
authlib==1.2.1
oauthlib==3.2.2

# Multi-Factor Authentication
pyotp==2.9.0
qrcode==7.4.2

# Device Fingerprinting
user-agents==2.2.0

# Anomaly Detection
scikit-learn==1.3.0
numpy==1.24.3

# Background Task Processing for Security Events
celery==5.3.4
kombu==5.3.4

# Message Queue for Security Alerts
pika==1.3.2

# Email Notifications for Security Events
Flask-Mail==0.9.1

# SMS Notifications for Security Alerts
twilio==8.10.0

# Webhook Support for Security Events
requests==2.31.0
httpx==0.25.0

# Configuration Management
configparser==6.0.0

# Time-based Operations
arrow==1.3.0
pendulum==2.1.2

# Caching for Security Data
flask-caching==2.1.0

# Database Migrations
Flask-Migrate==4.0.5

# Development and Testing Dependencies
pytest==7.4.2
pytest-flask==1.2.0
pytest-cov==4.1.0
pytest-mock==3.12.0
responses==0.23.3
factory-boy==3.3.0

# Load Testing for Security
locust==2.17.0

# Security Testing
bandit==1.7.5
safety==2.3.5

# Production Server
gunicorn==21.2.0

# Process Management
supervisor==4.2.5

# Health Checks
healthcheck==1.3.3

# Metrics Collection
statsd==4.0.1

# Built-in Python modules (listed for clarity):
# hashlib - Secure hash algorithms (built-in)
# hmac - Keyed-Hashing for Message Authentication (built-in)
# secrets - Generate secure random numbers (built-in)
# ssl - TLS/SSL wrapper for socket objects (built-in)
# socket - Low-level networking interface (built-in)
# threading - Thread-based parallelism (built-in)
# multiprocessing - Process-based parallelism (built-in)
# logging - Logging facility (built-in)
# warnings - Warning control (built-in)
# traceback - Print or retrieve a stack traceback (built-in)
# inspect - Inspect live objects (built-in)
# functools - Higher-order functions and operations on callable objects (built-in)
# contextlib - Utilities for with-statement contexts (built-in)
# weakref - Weak references (built-in)
# gc - Garbage Collector interface (built-in)
